# QuickCV - Resume Builder

A modern, full-stack resume builder application built with Next.js 15, TypeScript, and HeroUI. Create professional resumes with 12 beautiful templates, export to PDF, and build personal websites.

## Features

- 🎨 12 professional resume templates
- 📄 PDF export with production-ready generation
- 🌐 Personal website builder
- 🔒 Secure authentication with Clerk
- 📱 Responsive design and mobile-friendly
- 🌍 Multi-language support (English/Arabic)
- ☁️ Cloud storage for photos and files
- 💾 Real-time auto-save

## Technologies Used

- [Next.js 15](https://nextjs.org/docs/getting-started) - React framework with App Router
- [TypeScript](https://www.typescriptlang.org/) - Type-safe JavaScript
- [HeroUI v2](https://heroui.com/) - Modern React UI components
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [Drizzle ORM](https://orm.drizzle.team/) - TypeScript ORM for SQLite/Turso
- [Clerk](https://clerk.dev/) - User authentication and management
- [UploadThing](https://uploadthing.com/) - File upload service
- [Puppeteer](https://pptr.dev/) - PDF generation
- [Next-intl](https://next-intl-docs.vercel.app/) - Internationalization

## Quick Start

### Prerequisites

- Node.js 18+ or Bun
- SQLite (for local development)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd quickcv
```

2. Install dependencies:
```bash
bun install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Set up the database:
```bash
# Start local Turso database
bun run db:local

# Run migrations
bun run db:migrate

# Seed with sample data (optional)
bun run db/seed.ts
```

5. Start the development server:
```bash
bun run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## Production PDF Setup

For production PDF generation, configure a remote browser service:

### Option 1: Browserless (Recommended)
```bash
# Add to your production environment
BROWSER_WS_ENDPOINT=wss://chrome.browserless.io?token=YOUR_TOKEN
```

### Option 2: Docker Compose with Chrome
```yaml
services:
  chrome:
    image: browserless/chrome:latest
    ports:
      - "3001:3000"
    environment:
      - ENABLE_DEBUGGER=false
      - MAX_CONCURRENT_SESSIONS=10
```

Then set:
```bash
BROWSER_WS_ENDPOINT=ws://chrome:3000
```

## Development Commands

```bash
# Development server
bun run dev

# Build for production  
bun run build

# Database operations
bun run db:generate  # Generate migrations
bun run db:migrate   # Run migrations
bun run db:studio    # Open Drizzle Studio

# Linting and formatting
bun run lint
```

## Environment Variables

See `.env.example` for all available configuration options. Key variables:

- `CLERK_SECRET_KEY` - Clerk authentication
- `TURSO_CONNECTION_URL` - Database connection
- `UPLOADTHING_TOKEN` - File upload service
- `BROWSER_WS_ENDPOINT` - Remote browser for PDF generation

## License

Licensed under the [MIT license](https://github.com/heroui-inc/next-app-template/blob/main/LICENSE).
