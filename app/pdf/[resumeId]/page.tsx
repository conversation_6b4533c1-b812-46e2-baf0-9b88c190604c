import { COLOR_SCHEMES, ColorSchemeId } from "@/config/color-schemes";
import { getFullResumeQuery, getResumeQuery } from "@/lib/resume-queries";
import "@/styles/globals.css";
import { asc } from "drizzle-orm";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import { db } from "@/db";
import { templates } from "@/db/schema";
import { PDFTemplateRenderer } from "../../../components/features/resume/pdf-template-renderer";

export const metadata: Metadata = {
  title: "Resume PDF",
  robots: {
    index: false,
    follow: false,
  },
};

interface PDFPageProps {
  params: Promise<{ resumeId: string }>;
  searchParams: Promise<{ userId?: string; locale?: string }>;
}

async function getResumeForPDF(id: number, userId: string) {
  return await getFullResumeQuery(id, userId);
}

async function getTemplates() {
  return await db.select().from(templates).orderBy(asc(templates.id));
}

export default async function PDFPage({ params, searchParams }: PDFPageProps) {
  const { resumeId } = await params;
  const { userId, locale } = await searchParams;
  const id = parseInt(resumeId);

  if (Number.isNaN(id)) {
    notFound();
  }

  // Get locale from URL parameters, fallback to English
  const currentLocale = locale || "en";

  // Load messages for the locale
  let messages: any;
  try {
    messages = await getMessages({ locale: currentLocale });
  } catch (error) {
    console.error("Failed to load messages for PDF:", error);
    // Fallback to minimal English messages
    messages = {
      forms: {
        summary: "Summary",
        experience: "Experience",
        education: "Education",
        skills: "Skills",
        languages: "Languages",
        projects: "Projects",
        certifications: "Certifications",
        profiles: "Profiles",
        references: "References",
        volunteering: "Volunteering",
        awards: "Awards & Achievements",
        hobbies: "Hobbies",
      },
    };
  }

  try {
    const [resume, allTemplates] = await Promise.all([getResumeForPDF(id, userId ?? ""), getTemplates()]);

    if (!resume) {
      notFound();
    }

    // Find the template for this resume
    const template = allTemplates.find((t) => t.id === resume.templateId) || allTemplates[0];
    const resumeWithTemplate = { ...resume, template };

    // Get color scheme from resume data
    const schemeId = (resume.colorScheme || "blue") as ColorSchemeId;
    const colorScheme = COLOR_SCHEMES[schemeId] || COLOR_SCHEMES.blue;

    const cssVariables = {
      "--resume-background": colorScheme.background,
      "--resume-foreground": colorScheme.text,
      "--resume-primary": colorScheme.primary,
    } as React.CSSProperties;

    return (
      <NextIntlClientProvider messages={messages} locale={currentLocale}>
        <div className="pdf-page" style={cssVariables}>
          <style
            dangerouslySetInnerHTML={{
              __html: `
              @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
              @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
              @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap');
              
              @page {
                size: A4;
                margin: 0;
                padding: 0;
              }
              
              * {
                box-sizing: border-box;
              }
              
              body {
                margin: 0;
                padding: 0;
                background: white;
                font-family: system-ui, -apple-system, sans-serif;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                direction: ${currentLocale === "ar" ? "rtl" : "ltr"};
              }
              
              .pdf-page {
                width: 8.5in;
                min-height: 11in;
                margin: 0;
                padding: 0;
                background: white;
                position: relative;
                direction: ${currentLocale === "ar" ? "rtl" : "ltr"};
              }
              
              .no-print,
              .preview-controls {
                display: none !important;
              }
            `,
            }}
          />
          <PDFTemplateRenderer resume={resumeWithTemplate} />
        </div>
      </NextIntlClientProvider>
    );
  } catch (error) {
    console.error("Error rendering PDF page:", error);
    notFound();
  }
}
