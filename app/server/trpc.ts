import { initTRPC, TRPCError } from "@trpc/server";
import { getAuthenticatedUser } from "@/lib/auth-clerk";
import { Context } from "./context";
import {ZodError} from "zod";

const t = initTRPC.context<Context>().create({
  errorFormatter(opts) {
    const { shape, error } = opts;
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
            error.code === 'BAD_REQUEST' && error.cause instanceof ZodError
                ? error?.cause?.flatten()
                : null,
      },
    };
  },
});
const isAuthed = t.middleware(async ({ next, ctx }) => {
  if (!ctx.auth.userId) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  // Get or create user in database
  const user = await getAuthenticatedUser();
  if (!user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  return next({
    ctx: {
      auth: ctx.auth,
      user: user,
    },
  });
});
export const router = t.router;
export const publicProcedure = t.procedure;
export const protectedProcedure = t.procedure.use(isAuthed);
