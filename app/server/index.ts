import { resumesRouter } from "./routers/resumes";
import { templatesRouter } from "./routers/templates";
import { userRouter } from "./routers/user";
import { websitesRouter } from "./routers/websites";
import { router } from "./trpc";

export const appRouter = router({
  resumes: resumesRouter,
  templates: templatesRouter,
  user: userRouter,
  websites: websitesRouter,
});

export type AppRouter = typeof appRouter;
