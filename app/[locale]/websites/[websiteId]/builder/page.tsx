import { and, eq } from "drizzle-orm";
import { notFound } from "next/navigation";
import { WebsiteBuilderPage } from "@/components";
import { db } from "@/db";
import { resumes, websites, websiteTemplates } from "@/db/schema";
import { requireAuth } from "@/lib/auth-clerk";

interface PageProps {
  params: Promise<{
    locale: string;
    websiteId: string;
  }>;
}

export default async function WebsiteBuilderEditPage({ params }: PageProps) {
  const user = await requireAuth();
  const { websiteId: websiteIdParam } = await params;
  const websiteId = parseInt(websiteIdParam);

  const [website] = await db
    .select()
    .from(websites)
    .where(and(eq(websites.id, websiteId), eq(websites.userId, user.clerkId)))
    .limit(1);

  return <WebsiteBuilderPage website={website} />;
}
