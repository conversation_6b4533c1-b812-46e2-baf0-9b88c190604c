import { ResumeCustomizationDrawer } from "@/components/features/resume/ResumeCustomizationDrawer";
import { ResumeEditFormHeader } from "@/components/features/resume/ResumeEditFormHeader";
import { ResumeEditPageHeader } from "@/components/features/resume/ResumeEditPageHeader";
import { ResumeEditForm } from "@/components/features/resume/resume-edit-form";
import { ResumePreview } from "@/components/features/resume/resume-preview";

export default async function EditResumePage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params; // Destructure id from params
  const resumeId = Number(id);

  return (
    <div>
      <div className="container mx-auto min-h-screen flex flex-col">
        <ResumeEditPageHeader />

        {/* Main Content Grid */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-12 gap-6 p-6 min-h-0">
          {/* Preview Panel */}
          <div className="order-2 lg:order-1 col-span-7 min-h-0">
            <div className="h-full max-h-[calc(100vh-8rem)] bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
              <ResumePreview className="h-full" resumeId={resumeId} showControls={true} showTemplateSelector={true} />
            </div>
          </div>

          {/* Edit Form Panel */}
          <div className="order-1 lg:order-2 col-span-5 min-h-0">
            <div className="h-full max-h-[calc(100vh-8rem)] bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 flex flex-col">
              <ResumeEditFormHeader />

              {/* Form Content - Scrollable */}
              <div className="flex-1 overflow-y-auto overflow-x-hidden">
                <ResumeEditForm resumeId={resumeId} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Customization Drawer */}
      <ResumeCustomizationDrawer resumeId={resumeId} />
    </div>
  );
}
