import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import CTASection from "@/components/features/homepage/cta-section";
import FeaturesSection from "@/components/features/homepage/features-section";
import HeroSection from "@/components/features/homepage/hero-section";
import PricingSection from "@/components/features/homepage/pricing-section";
import StatsSection from "@/components/features/homepage/stats-section";
import StructuredData from "@/components/features/homepage/structured-data";
import TemplatesShowcase from "@/components/features/homepage/templates-showcase";
import TestimonialsSection from "@/components/features/homepage/testimonials-section";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("homepage.seo");

  return {
    title: t("title"),
    description: t("description"),
    keywords: t("keywords"),
    openGraph: {
      title: t("title"),
      description: t("description"),
      type: "website",
      url: "https://quickcv.app",
      images: [
        {
          url: "/og-image.jpg",
          width: 1200,
          height: 630,
          alt: t("title"),
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: t("title"),
      description: t("description"),
      images: ["/og-image.jpg"],
    },
    metadataBase: new URL("https://quickcv.app"),
    alternates: {
      canonical: "https://quickcv.app",
      languages: {
        "en-US": "https://quickcv.app/en",
        "ar-SA": "https://quickcv.app/ar",
      },
    },
  };
}

export default async function Home() {
  return (
    <>
      <StructuredData />
      <HeroSection />
      <FeaturesSection />
      <TemplatesShowcase />
      <PricingSection />
      <StatsSection />
      <TestimonialsSection />
      <CTASection />
    </>
  );
}
