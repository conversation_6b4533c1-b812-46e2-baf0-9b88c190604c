"use client";

import { AppProgressProvider as ProgressProvider } from "@bprogress/next";
import { useRouter } from "@bprogress/next/app";
import { ClerkProvider } from "@clerk/nextjs";
import { HeroUIProvider } from "@heroui/react";
import type { ThemeProviderProps } from "next-themes";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import * as React from "react";
import { Toaster } from "react-hot-toast";
import TRPCProvider from "@/app/_trpc/Provider";
import { GlobalLoadingOverlay } from "@/components/shared";
export interface ProvidersProps {
  children: React.ReactNode;
  themeProps?: ThemeProviderProps;
}

declare module "@react-types/shared" {
  interface RouterConfig {
    routerOptions: NonNullable<Parameters<ReturnType<typeof useRouter>["push"]>[1]>;
  }
}

function InnerProviders({ children, themeProps }: ProvidersProps) {
  const router = useRouter();

  return (
    <ClerkProvider>
      <TRPCProvider>
        <HeroUIProvider navigate={router.push}>
          <NextThemesProvider {...themeProps}>
            <div>
              <Toaster position="top-right" reverseOrder={false} />
            </div>
            <GlobalLoadingOverlay />
            {children}
          </NextThemesProvider>
        </HeroUIProvider>
      </TRPCProvider>
    </ClerkProvider>
  );
}

export function Providers({ children, themeProps }: ProvidersProps) {
  return (
    <ProgressProvider height="4px" color="oklch(58.5% 0.233 277.117)" options={{ showSpinner: false }} shallowRouting>
      <InnerProviders themeProps={themeProps}>{children}</InnerProviders>
    </ProgressProvider>
  );
}
