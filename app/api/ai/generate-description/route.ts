import { NextRequest, NextResponse } from "next/server";
import { getTranslations } from "next-intl/server";
import { requireAuth } from "@/lib/auth-clerk";
import { OllamaClientError, ollamaClient } from "@/lib/ollama-client";
import { addRateLimitHeaders, RateLimiterError, rateLimitManager } from "@/lib/rate-limiter";

interface GenerateDescriptionRequest {
  contextType:
    | "experience"
    | "project"
    | "education"
    | "award"
    | "certification"
    | "hobby"
    | "volunteering"
    | "reference"
    | "bio";
  resumeContext: {
    firstName?: string;
    lastName?: string;
    jobTitle?: string;
    bio?: string;
    skills?: Array<{ name: string; category?: string }>;
  };
  itemContext: {
    title?: string;
    company?: string;
    organization?: string;
    institution?: string;
    startDate?: string;
    endDate?: string;
    isCurrent?: boolean;
    role?: string;
    degree?: string;
    fieldOfStudy?: string;
    issuer?: string;
    currentDescription?: string;
  };
  options: {
    tone: "professional" | "casual" | "technical";
    length: "short" | "medium" | "detailed";
    language: "en" | "ar";
  };
}

export async function POST(request: NextRequest) {
  let language = "en"; // Default language for error messages

  try {
    // Verify authentication
    const user = await requireAuth();

    // Check rate limits
    const isPremium = user.isPremium || false; // Assuming isPremium field exists
    const rateLimitResult = await rateLimitManager.checkAILimit(user.clerkId, isPremium);

    const body: GenerateDescriptionRequest = await request.json();
    const { contextType, resumeContext, itemContext, options } = body;

    // Set language for error messages
    if (options?.language) {
      language = options.language;
    }

    // Get error translations
    const tErrors = await getTranslations({ locale: language, namespace: "ai.errors" });

    // Validate required fields
    if (!contextType || !options) {
      return NextResponse.json({ error: tErrors("missing_fields") }, { status: 400 });
    }

    // Generate context-aware prompt
    const prompt = await generatePrompt(contextType, resumeContext, itemContext, options);

    // Call local LLM service
    const generatedDescription = await callLocalLLM(prompt, options);

    // Create response with rate limit headers
    const response = NextResponse.json({
      success: true,
      description: generatedDescription,
      contextType,
      prompt: process.env.NODE_ENV === "development" ? prompt : undefined,
    });

    // Add rate limit headers
    addRateLimitHeaders(response.headers, rateLimitResult, 60 * 60 * 1000); // 1 hour window

    return response;
  } catch (error) {
    console.error("AI description generation error:", error);

    // Get error translations with fallback
    const tErrors = await getTranslations({ locale: language, namespace: "ai.errors" });

    // Handle rate limit errors
    if (error instanceof RateLimiterError) {
      const response = NextResponse.json(
        {
          error: error.message,
          code: "RATE_LIMIT_EXCEEDED",
          retryAfter: Math.ceil((error.rateLimitResult.resetTime - Date.now()) / 1000),
        },
        { status: 429 },
      );

      // Add rate limit headers
      addRateLimitHeaders(response.headers, error.rateLimitResult, 60 * 60 * 1000);
      return response;
    }

    // Handle specific LLM errors
    if (error instanceof OllamaClientError) {
      const status = error.status || 500;
      const message = error.code === "MODEL_NOT_FOUND" ? tErrors("model_not_found") : tErrors("service_unavailable");

      return NextResponse.json({ error: message, code: error.code }, { status });
    }

    return NextResponse.json({ error: tErrors("failed_generation") }, { status: 500 });
  }
}

async function generatePrompt(
  contextType: string,
  resumeContext: any,
  itemContext: any,
  options: any,
): Promise<string> {
  const { tone, length, language } = options;

  // Get translations for different namespaces
  const tGuidance = await getTranslations({ locale: language, namespace: "ai.guidance" });
  const tPrompts = await getTranslations({ locale: language, namespace: "ai.prompts" });

  // Get base prompt using translations
  let basePrompt = tPrompts(`base.${contextType}`);

  // Add tone and length guidance using translations
  const toneGuidance = tGuidance(`tone.${tone}`);
  const lengthGuidance = tGuidance(`length.${length}`);

  basePrompt += ` ${toneGuidance} ${lengthGuidance}.`;

  // Add context-specific details
  switch (contextType) {
    case "experience":
      if (itemContext.title && itemContext.company) {
        basePrompt += `\n\n${tPrompts("labels.position")}: ${itemContext.title}\n${tPrompts("labels.company")}: ${itemContext.company}`;
      }
      if (itemContext.startDate) {
        const periodKey = itemContext.isCurrent ? "to_present" : "to_end";
        const period = tPrompts(`date_ranges.${periodKey}`, {
          startDate: itemContext.startDate,
          endDate: itemContext.endDate || "",
        });
        basePrompt += `\n${tPrompts("labels.period")}: ${period}`;
      }
      break;

    case "project":
      if (itemContext.title) {
        basePrompt += `\n\n${tPrompts("labels.project")}: ${itemContext.title}`;
      }
      if (itemContext.client) {
        basePrompt += `\n${tPrompts("labels.client")}: ${itemContext.client}`;
      }
      break;

    case "education":
      if (itemContext.degree && itemContext.institution) {
        basePrompt += `\n\n${tPrompts("labels.degree")}: ${itemContext.degree}\n${tPrompts("labels.institution")}: ${itemContext.institution}`;
      }
      if (itemContext.fieldOfStudy) {
        basePrompt += `\n${tPrompts("labels.field_of_study")}: ${itemContext.fieldOfStudy}`;
      }
      break;

    case "award":
    case "certification":
      if (itemContext.title && itemContext.issuer) {
        basePrompt += `\n\n${tPrompts("labels.title")}: ${itemContext.title}\n${tPrompts("labels.issuer")}: ${itemContext.issuer}`;
      }
      break;

    case "volunteering":
      if (itemContext.role && itemContext.organization) {
        basePrompt += `\n\n${tPrompts("labels.role")}: ${itemContext.role}\n${tPrompts("labels.organization")}: ${itemContext.organization}`;
      }
      break;

    case "bio":
      if (resumeContext.jobTitle) {
        basePrompt += `\n\n${tPrompts("labels.job_title")}: ${resumeContext.jobTitle}`;
      }
      break;
  }

  // Add resume context if available
  if (resumeContext.skills && resumeContext.skills.length > 0) {
    const skillsList = resumeContext.skills.map((skill: any) => skill.name).join(", ");
    basePrompt += `\n\n${tPrompts("labels.relevant_skills")}: ${skillsList}`;
  }

  // Add current description for improvement
  if (itemContext.currentDescription) {
    basePrompt += `\n\n${tPrompts("labels.current_description")}: ${itemContext.currentDescription}`;
  }

  // Add formatting instructions
  basePrompt += `\n\n${tPrompts("formatting")}`;

  return basePrompt;
}

async function callLocalLLM(prompt: string, options: any): Promise<string> {
  try {
    // Get error translations
    const tErrors = await getTranslations({ locale: options.language || "en", namespace: "ai.errors" });

    // Health check first
    const isHealthy = await ollamaClient.healthCheck();
    if (!isHealthy) {
      throw new OllamaClientError(tErrors("ollama_unavailable"), 503, "SERVICE_UNAVAILABLE");
    }

    // Get model from environment or use default
    const model = process.env.OLLAMA_MODEL || "llama2";

    // Configure generation settings based on context
    const temperature = getTemperatureForTone(options.tone);
    const maxTokens = getMaxTokensForLength(options.length);

    // Generate response using Ollama
    const response = await ollamaClient.generate(prompt, {
      model,
      temperature,
      maxTokens,
      stopSequences: ["Human:", "Assistant:", "\n\n---"],
    });

    // Post-process the response to ensure it's in HTML format
    return formatResponseAsHTML(response, options.language);
  } catch (error) {
    console.error("Local LLM call failed:", error);

    // Get error translations for error handling
    const tErrors = await getTranslations({ locale: options.language || "en", namespace: "ai.errors" });

    // Re-throw OllamaClientError as-is
    if (error instanceof OllamaClientError) {
      throw error;
    }

    // Wrap other errors
    throw new OllamaClientError(
      tErrors("generation_failed", { error: error instanceof Error ? error.message : "Unknown error" }),
      500,
      "GENERATION_FAILED",
    );
  }
}

/**
 * Get temperature setting based on requested tone
 */
function getTemperatureForTone(tone: string): number {
  switch (tone) {
    case "professional":
      return 0.5; // More focused and consistent
    case "casual":
      return 0.8; // More creative and varied
    case "technical":
      return 0.3; // Very focused and precise
    default:
      return 0.7; // Balanced default
  }
}

/**
 * Get max tokens based on requested length
 */
function getMaxTokensForLength(length: string): number {
  switch (length) {
    case "short":
      return 200; // ~150 words
    case "medium":
      return 400; // ~300 words
    case "detailed":
      return 800; // ~600 words
    default:
      return 400; // Default to medium
  }
}

/**
 * Format the LLM response as HTML
 */
function formatResponseAsHTML(response: string, language: string = "en"): string {
  // Remove any potential markdown formatting
  let formatted = response
    .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>") // Bold
    .replace(/\*(.*?)\*/g, "<em>$1</em>") // Italic
    .replace(/^- (.*$)/gim, "<li>$1</li>") // List items
    .replace(/^\d+\. (.*$)/gim, "<li>$1</li>"); // Numbered list items

  // Handle lists
  if (formatted.includes("<li>")) {
    const listItems = formatted.match(/<li>.*?<\/li>/g);
    if (listItems) {
      const listContent = listItems.join("");
      formatted = formatted
        .replace(/<li>.*?<\/li>/g, "")
        .replace(/\s+/g, " ")
        .trim();
      formatted += `<ul>${listContent}</ul>`;
    }
  }

  // Split into paragraphs if not already formatted
  if (!formatted.includes("<p>") && !formatted.includes("<ul>")) {
    const paragraphs = formatted
      .split("\n\n")
      .filter((p) => p.trim())
      .map((p) => `<p>${p.trim()}</p>`)
      .join("");
    formatted = paragraphs;
  } else if (!formatted.includes("<p>")) {
    // Wrap in paragraph if it has other HTML but no paragraphs
    formatted = `<p>${formatted}</p>`;
  }

  // Clean up any extra whitespace
  formatted = formatted
    .replace(/\s*<\/p>\s*<p>\s*/g, "</p><p>")
    .replace(/\s*<\/li>\s*<li>\s*/g, "</li><li>")
    .trim();

  return formatted;
}
