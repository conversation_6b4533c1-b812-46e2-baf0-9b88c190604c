import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import { convertParsedToResumeSection, PDFResumeParser } from "@/lib/pdf-resume-parser";

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the uploaded file
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Validate file type
    if (file.type !== "application/pdf") {
      return NextResponse.json({ error: "Only PDF files are supported" }, { status: 400 });
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: "File size must be less than 10MB" }, { status: 400 });
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Parse the PDF
    const parser = new PDFResumeParser();
    try {
      const parsedData = await parser.parsePDFResume(buffer);
      const resumeData = convertParsedToResumeSection(parsedData);

      return NextResponse.json({
        success: true,
        data: resumeData,
        summary: {
          personalInfo: !!(resumeData.firstName || resumeData.lastName || resumeData.email),
          experiences: resumeData.experiences?.length || 0,
          educations: resumeData.educations?.length || 0,
          skills: resumeData.skills?.length || 0,
        },
      });
    } catch (parseError) {
      console.error("PDF parsing specific error:", parseError);
      return NextResponse.json(
        {
          error: "Failed to parse PDF content",
          details: parseError instanceof Error ? parseError.message : "Unable to extract readable text from PDF",
        },
        { status: 422 },
      );
    } finally {
      await parser.close();
    }
  } catch (error) {
    console.error("PDF parsing error:", error);
    return NextResponse.json(
      {
        error: "Failed to parse PDF resume",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
