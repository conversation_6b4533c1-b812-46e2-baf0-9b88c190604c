"use client";

import { useTranslations } from "next-intl";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { useUploadThing } from "@/lib/uploadthing";

interface UseFileUploadProps {
  endpoint: "imageUploader" | "resumePhotoUploader";
  onSuccess?: (res: { url: string }[]) => void;
  onError?: (error: Error) => void;
}

export function useFileUpload({ endpoint, onSuccess, onError }: UseFileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const t = useTranslations("validation");

  const { startUpload } = useUploadThing(endpoint, {
    onClientUploadComplete: (res) => {
      setIsUploading(false);
      setUploadProgress(0);
      toast.success(t("upload_success"));
      onSuccess?.(res);
    },
    onUploadError: (error: Error) => {
      setIsUploading(false);
      setUploadProgress(0);
      toast.error(error.message);
      onError?.(error);
    },
    onUploadBegin: () => {
      setIsUploading(true);
      setUploadProgress(0);
    },
    onUploadProgress: (progress) => {
      setUploadProgress(progress);
    },
  });

  const uploadFiles = async (files: File[]) => {
    if (files.length === 0) return;

    try {
      await startUpload(files);
    } catch (error) {
      console.error("Upload error:", error);
      setIsUploading(false);
      setUploadProgress(0);
      toast.error(t("upload_error"));
      onError?.(error as Error);
    }
  };

  const uploadSingleFile = async (file: File) => {
    await uploadFiles([file]);
  };

  return {
    isUploading,
    uploadProgress,
    uploadFiles,
    uploadSingleFile,
  };
}
