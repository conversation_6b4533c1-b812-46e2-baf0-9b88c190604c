# AI Writing Assistant Feature Documentation

## Overview

The AI Writing Assistant feature provides intelligent content generation for resume descriptions using a local Large Language Model (LLM) via Ollama. This feature helps users create professional, contextually-aware descriptions for various resume sections.

## Features

- **Local LLM Integration**: Uses Ollama for privacy-focused, local AI processing
- **Context-Aware Generation**: Incorporates resume data to generate relevant content
- **Multi-Language Support**: Supports English and Arabic content generation
- **Customizable Output**: Adjustable tone, length, and style options
- **Rate Limiting**: Prevents abuse with tier-based usage limits
- **Rich Text Integration**: Seamlessly integrates with TipTap editor
- **Error Handling**: Comprehensive error management and fallback strategies

## Architecture

### Core Components

1. **API Endpoint**: `/app/api/ai/generate-description/route.ts`
2. **Client Service**: `/lib/ai-service.ts`
3. **Ollama Client**: `/lib/ollama-client.ts`
4. **Rate Limiter**: `/lib/rate-limiter.ts`
5. **Configuration**: `/lib/ai-config.ts`
6. **UI Components**: AI modal and rich editor integration

### Data Flow

```
Rich Text Editor → AI Button → AI Modal → AI Service → API Endpoint → Ollama → Response → Editor
```

## Setup and Installation

### Prerequisites

1. **Ollama Installation**
   ```bash
   # Install Ollama (macOS)
   brew install ollama
   
   # Install Ollama (Linux)
   curl -fsSL https://ollama.com/install.sh | sh
   
   # Install Ollama (Windows)
   # Download from https://ollama.com/download
   ```

2. **Download AI Model**
   ```bash
   # Download default model (Llama 2)
   ollama pull llama2
   
   # Alternative models
   ollama pull mistral      # Fast and efficient
   ollama pull codellama    # Code-focused model
   ollama pull llama2:13b   # Better quality, larger size
   ```

3. **Start Ollama Service**
   ```bash
   # Start Ollama server
   ollama serve
   
   # Verify installation
   curl http://localhost:11434/api/tags
   ```

### Environment Configuration

Add the following variables to your `.env.local` file:

```env
# ===================================
# AI CONFIGURATION
# ===================================

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2
OLLAMA_TIMEOUT=30000
OLLAMA_MAX_RETRIES=3

# AI Rate Limiting Configuration
AI_RATE_LIMIT_FREE_HOURLY=10        # Free tier: 10 requests per hour
AI_RATE_LIMIT_PREMIUM_HOURLY=100    # Premium tier: 100 requests per hour
AI_RATE_LIMIT_BURST_MINUTE=5        # Burst protection: 5 requests per minute
AI_RATE_LIMIT_GLOBAL_HOURLY=1000    # Global API limit: 1000 requests per hour

# AI Generation Settings
AI_DEFAULT_TEMPERATURE=0.7           # Default creativity level (0.0-1.0)
AI_MAX_TOKENS_SHORT=200             # Short descriptions (~150 words)
AI_MAX_TOKENS_MEDIUM=400            # Medium descriptions (~300 words)
AI_MAX_TOKENS_DETAILED=800          # Detailed descriptions (~600 words)

# AI Monitoring and Analytics
AI_ENABLE_USAGE_TRACKING=true        # Track AI usage for analytics
AI_ENABLE_PERFORMANCE_MONITORING=true # Monitor AI performance metrics
AI_LOG_LEVEL=info                    # Logging level: debug, info, warn, error

# AI Cache Configuration (Optional)
AI_ENABLE_RESPONSE_CACHE=false       # Cache AI responses to improve performance
AI_CACHE_TTL_SECONDS=3600           # Cache time-to-live in seconds (1 hour)
AI_CACHE_MAX_ENTRIES=1000           # Maximum number of cached responses
```

## Usage Guide

### Basic Usage

1. **Access AI Feature**: Click the magic wand icon (🪄) in any rich text editor
2. **Configure Options**: Select tone, length, and language preferences
3. **Generate Content**: Click "Generate Description" to create 3 variations
4. **Select Content**: Choose your preferred variation to insert into the editor

### Context Types

The AI assistant supports the following resume sections:

- **Experience**: Work history and job descriptions
- **Projects**: Personal and professional project descriptions
- **Education**: Academic background and achievements
- **Awards**: Recognition and achievements
- **Certifications**: Professional certifications and licenses
- **Volunteering**: Community service and volunteer work
- **Bio**: Professional summary and personal statement
- **Hobbies**: Personal interests and activities
- **References**: Professional reference descriptions

### Generation Options

#### Tone Options
- **Professional**: Formal, business-appropriate language
- **Casual**: Friendly, approachable tone
- **Technical**: Specialized, industry-specific terminology

#### Length Options
- **Short**: 2-3 sentences (~150 words)
- **Medium**: 4-6 sentences (~300 words)
- **Detailed**: 7-10 sentences (~600 words)

#### Language Support
- **English (en)**: Default language
- **Arabic (ar)**: Right-to-left text support

## API Documentation

### Endpoint: POST `/api/ai/generate-description`

#### Request Body
```typescript
{
  contextType: "experience" | "project" | "education" | "award" | "certification" | "hobby" | "volunteering" | "reference" | "bio";
  resumeContext: {
    firstName?: string;
    lastName?: string;
    jobTitle?: string;
    bio?: string;
    skills?: Array<{ name: string; category?: string }>;
  };
  itemContext: {
    title?: string;
    company?: string;
    organization?: string;
    institution?: string;
    startDate?: string;
    endDate?: string;
    isCurrent?: boolean;
    role?: string;
    degree?: string;
    fieldOfStudy?: string;
    issuer?: string;
    currentDescription?: string;
  };
  options: {
    tone: "professional" | "casual" | "technical";
    length: "short" | "medium" | "detailed";
    language: "en" | "ar";
  };
}
```

#### Response
```typescript
{
  success: boolean;
  description: string;
  contextType: string;
  prompt?: string; // Only in development mode
}
```

#### Error Responses
```typescript
// Rate limit exceeded (429)
{
  error: string;
  code: "RATE_LIMIT_EXCEEDED";
  retryAfter: number; // Seconds until reset
}

// LLM service error (503)
{
  error: string;
  code: "SERVICE_UNAVAILABLE" | "MODEL_NOT_FOUND" | "GENERATION_FAILED";
}
```

#### Rate Limit Headers
```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 7
X-RateLimit-Reset: 1642694400
X-RateLimit-Window: 3600
```

## Configuration Options

### Model Selection

Choose from recommended models based on your needs:

| Model | Use Case | Size | Description |
|-------|----------|------|-------------|
| `llama2` | General purpose | ~3.8GB | Balanced performance |
| `llama2:13b` | High quality | ~7.3GB | Better responses, slower |
| `mistral` | Production | ~4.1GB | Fast and efficient |
| `codellama` | Technical content | ~3.8GB | Code-focused model |
| `neural-chat` | Conversational | ~4.1GB | Optimized for chat |
| `starling-lm` | Advanced | ~4.1GB | High-quality reasoning |

### Rate Limiting Configuration

#### Rate Limit Tiers

| Tier | Hourly Limit | Burst Limit | Description |
|------|-------------|-------------|-------------|
| Free | 10 requests | 5/minute | Default tier |
| Premium | 100 requests | 5/minute | Paid subscribers |
| Global | 1000 requests | N/A | System-wide limit |

#### Customization
```typescript
// Modify rate limits in ai-config.ts
rateLimit: {
  freeHourly: 10,        // Free tier hourly limit
  premiumHourly: 100,    // Premium tier hourly limit
  burstMinute: 5,        // Burst protection per minute
  globalHourly: 1000,    // Global API limit
}
```

### Generation Settings

#### Temperature Control
```typescript
// Temperature by tone
professional: 0.5,  // More focused and consistent
casual: 0.8,        // More creative and varied
technical: 0.3,     // Very focused and precise
```

#### Token Limits
```typescript
// Token limits by length
short: 200,      // ~150 words
medium: 400,     // ~300 words
detailed: 800,   // ~600 words
```

## Error Handling

### Common Errors

1. **Ollama Not Running**
   ```
   Error: Ollama service is not available
   Solution: Start Ollama with `ollama serve`
   ```

2. **Model Not Found**
   ```
   Error: Model "llama2" not found
   Solution: Download model with `ollama pull llama2`
   ```

3. **Rate Limit Exceeded**
   ```
   Error: Rate limit exceeded
   Solution: Wait for reset time or upgrade to premium
   ```

4. **Network Timeout**
   ```
   Error: Request timeout
   Solution: Increase OLLAMA_TIMEOUT or check network
   ```

### Error Recovery

The system implements multiple fallback strategies:

1. **Automatic Retry**: Failed requests are retried up to 3 times with exponential backoff
2. **Health Checks**: Service availability is verified before each request
3. **Graceful Degradation**: Clear error messages guide users to solutions
4. **Rate Limit Headers**: Provide information about limits and reset times

## Performance Optimization

### Caching (Optional)

Enable response caching for improved performance:

```env
AI_ENABLE_RESPONSE_CACHE=true
AI_CACHE_TTL_SECONDS=3600
AI_CACHE_MAX_ENTRIES=1000
```

### Model Optimization

1. **Model Selection**: Choose models based on your performance/quality needs
2. **Token Limits**: Adjust token limits based on your use case
3. **Temperature**: Lower temperatures for faster, more consistent responses
4. **Timeout Settings**: Balance between reliability and responsiveness

### Resource Management

1. **Memory**: Larger models require more system memory
2. **CPU**: GPU acceleration improves generation speed
3. **Network**: Local processing eliminates network latency
4. **Storage**: Models require significant disk space

## Monitoring and Analytics

### Usage Tracking

Enable usage tracking to monitor AI feature adoption:

```env
AI_ENABLE_USAGE_TRACKING=true
AI_ENABLE_PERFORMANCE_MONITORING=true
AI_LOG_LEVEL=info
```

### Metrics Collected

- Request volume and frequency
- Generation success/failure rates
- Response times and latency
- Model performance statistics
- User engagement patterns
- Error rates and types

### Performance Monitoring

Monitor key performance indicators:

- **Response Time**: Average time to generate descriptions
- **Success Rate**: Percentage of successful generations
- **Error Rate**: Frequency and types of errors
- **Resource Usage**: Memory, CPU, and storage utilization
- **User Satisfaction**: Quality metrics and user feedback

## Security Considerations

### Data Privacy

1. **Local Processing**: All AI processing happens locally, no data sent to external services
2. **No Data Persistence**: Generated content is not stored by the AI service
3. **User Data Protection**: Resume data is only used for context generation
4. **Session Security**: All requests require authenticated user sessions

### Rate Limiting Security

1. **Abuse Prevention**: Rate limits prevent excessive API usage
2. **Resource Protection**: Protects server resources from overload
3. **Fair Usage**: Ensures equitable access for all users
4. **Scalability**: Maintains performance under high load

### Model Security

1. **Model Validation**: Only approved models are supported
2. **Input Sanitization**: User inputs are validated and sanitized
3. **Output Filtering**: Generated content is processed for safety
4. **Access Control**: Feature requires user authentication

## Troubleshooting

### Installation Issues

**Problem**: Ollama installation fails
**Solution**: 
- Check system requirements
- Use official installation methods
- Verify PATH environment variable

**Problem**: Model download fails
**Solution**:
- Check internet connection
- Verify disk space availability
- Try alternative model

### Runtime Issues

**Problem**: AI feature not available
**Solution**:
- Verify Ollama is running: `ps aux | grep ollama`
- Check Ollama health: `curl http://localhost:11434/api/tags`
- Restart Ollama service: `ollama serve`

**Problem**: Slow response times
**Solution**:
- Use smaller models (mistral instead of llama2:13b)
- Reduce token limits
- Lower temperature settings
- Enable GPU acceleration

**Problem**: Poor quality responses
**Solution**:
- Use larger models (llama2:13b instead of llama2)
- Increase temperature for more creativity
- Provide more context in item details
- Try specialized models (codellama for technical content)

### Configuration Issues

**Problem**: Environment variables not loading
**Solution**:
- Verify `.env.local` file exists
- Check variable names match exactly
- Restart development server
- Clear Next.js cache

**Problem**: Rate limits too restrictive
**Solution**:
- Adjust limits in environment variables
- Implement premium tier logic
- Monitor usage patterns
- Optimize generation requests

## Development Guide

### Adding New Context Types

1. **Update Types**: Add new context type to `types/ai.ts`
2. **Update API**: Add handling in `generatePrompt()` function
3. **Update Translation**: Add Arabic translation if needed
4. **Update UI**: Add new context type to form options

### Extending Generation Options

1. **Add New Tone**: Update tone options in configuration
2. **Implement Logic**: Add tone handling in `getTemperatureForTone()`
3. **Update UI**: Add new option to modal interface
4. **Test Integration**: Verify new options work correctly

### Custom Model Integration

1. **Model Configuration**: Add model to recommended list
2. **Validation**: Implement model availability checking
3. **Optimization**: Adjust settings for new model
4. **Documentation**: Update model comparison table

## Testing

### Manual Testing

1. **Feature Availability**: Verify AI button appears in editors
2. **Generation Quality**: Test with various context types
3. **Error Handling**: Test with Ollama stopped
4. **Rate Limiting**: Test with multiple rapid requests
5. **Language Support**: Test Arabic content generation

### Automated Testing

```bash
# Test Ollama connection
curl -X GET http://localhost:11434/api/tags

# Test API endpoint
curl -X POST http://localhost:3000/api/ai/generate-description \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "contextType": "experience",
    "resumeContext": {"jobTitle": "Software Engineer"},
    "itemContext": {"title": "Senior Developer", "company": "TechCorp"},
    "options": {"tone": "professional", "length": "medium", "language": "en"}
  }'

# Test rate limiting
for i in {1..15}; do
  curl -X POST http://localhost:3000/api/ai/generate-description \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer your-token" \
    -d '{"contextType": "bio", "resumeContext": {}, "itemContext": {}, "options": {"tone": "professional", "length": "short", "language": "en"}}'
done
```

### Performance Testing

```bash
# Monitor Ollama performance
ollama ps

# Check system resources
htop

# Monitor API response times
curl -w "@curl-format.txt" -X POST http://localhost:3000/api/ai/generate-description
```

## Contributing

### Code Style

- Follow existing TypeScript patterns
- Use proper error handling
- Include comprehensive JSDoc comments
- Follow security best practices

### Testing Requirements

- Manual testing for new features
- Error condition testing
- Performance impact assessment
- Security vulnerability review

### Documentation Updates

- Update this document for new features
- Include code examples
- Update troubleshooting guide
- Maintain API documentation accuracy

## Support

### Getting Help

1. **Documentation**: Review this guide thoroughly
2. **Issues**: Check GitHub issues for known problems  
3. **Community**: Join Ollama community discussions
4. **Logs**: Check application and Ollama logs for errors

### Reporting Issues

When reporting issues, include:

- System information (OS, Node.js version)
- Ollama version and model information
- Error messages and stack traces
- Steps to reproduce the issue
- Configuration settings (sanitized)

---

**Version**: 1.0.0  
**Last Updated**: January 2024  
**Author**: QuickCV AI Team