@import "tailwindcss";
@plugin '../hero.ts';
/* Note: You may need to change the path to fit your project structure */
@source '../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}';
@custom-variant dark (&:is(.dark *));
 @config "../tailwind.config.js";

 .wysiwyg {
  @apply max-w-none prose-headings:mb-2 prose-headings:mt-0 prose-p:mb-2 prose-p:mt-0 prose-p:leading-normal prose-a:break-all prose-ol:mb-2 prose-ol:mt-0 prose-ul:mb-2 prose-ul:mt-0 prose-li:mb-2 prose-li:mt-0 prose-li:leading-normal prose-img:mb-2 prose-img:mt-0 prose-hr:mb-2 prose-hr:mt-0;
}

/* RTL Support with improved Arabic fonts */
[dir="rtl"] {
  font-family: var(--font-arabic), "Noto Sans Arabic", sans-serif;
}

/* Contact info should remain LTR for URLs and emails */
[dir="rtl"] .contact-info {
  direction: ltr;
  text-align: left;
}

[dir="rtl"] .contact-info a {
  direction: ltr;
}