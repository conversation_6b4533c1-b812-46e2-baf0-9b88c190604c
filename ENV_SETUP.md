# Environment Variables Setup

This project uses different environment files for local development and production environments.

## Environment Files

- `.env.local` - Local development environment variables
- `.env.production` - Production environment variables
- `.env.example` - Example template for environment variables

## Setup Instructions

### Local Development

1. Copy the `.env.local` file to `.env`:
   ```bash
   cp .env.local .env
   ```

2. The local environment is pre-configured with development credentials

### Production Deployment

1. Copy `.env.production` and replace all placeholder values with your actual production credentials:
   ```bash
   cp .env.production .env
   ```

2. Update the following production values:
   - **Clerk**: Replace with production Clerk keys
   - **Turso Database**: Add your production database URL and auth token
   - **Base URL**: Update to your production domain
   - **UploadThing**: Add production upload credentials
   - **PostHog**: Add production analytics keys
   - **Browserless**: Configure production PDF generation endpoint
   - **Paddle**: Add production payment credentials

## Environment Variables Reference

### Authentication (Clerk)
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` - Public Clerk key
- `CLERK_SECRET_KEY` - Secret Clerk key (server-side only)

### Database (Turso)
- `TURSO_CONNECTION_URL` - Database connection URL
- `TURSO_AUTH_TOKEN` - Database authentication token (production only)

### File Uploads (UploadThing)
- `UPLOADTHING_TOKEN` - UploadThing API token
- `UPLOADTHING_APP_ID` - UploadThing application ID

### Analytics (PostHog)
- `NEXT_PUBLIC_POSTHOG_KEY` - PostHog public key
- `NEXT_PUBLIC_POSTHOG_HOST` - PostHog host URL

### PDF Generation
- `BROWSER_WS_ENDPOINT` - Browserless WebSocket endpoint

### Payments (Paddle)
- `NEXT_PUBLIC_PADDLE_ENVIRONMENT` - 'sandbox' or 'production'
- `NEXT_PUBLIC_PADDLE_CLIENT_TOKEN` - Paddle client token
- `PADDLE_API_KEY` - Paddle API key (server-side only)
- `PADDLE_WEBHOOK_SECRET` - Paddle webhook secret
- `NEXT_PUBLIC_PADDLE_LIFETIME_PRICE_ID` - Lifetime plan price ID
- `NEXT_PUBLIC_PADDLE_LIFETIME_PRODUCT_ID` - Lifetime plan product ID

## Security Notes

- Never commit `.env`, `.env.local`, or `.env.production` files
- All files containing actual credentials are gitignored
- Use `.env.example` as a template for team members
- Production credentials should be stored securely in your deployment platform