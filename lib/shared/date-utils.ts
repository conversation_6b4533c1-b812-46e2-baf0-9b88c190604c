export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    day: "numeric",
    month: "short",
    year: "numeric",
  }).format(date);
};

export const formatDateForLocale = (
  dateString: string,
  locale: string = "en-US",
  options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
  },
) => {
  if (!dateString) return "";

  const date = new Date(dateString);

  if (locale === "ar") {
    return date.toLocaleDateString("ar-SA", options);
  }

  return date.toLocaleDateString(locale, options);
};

export const formatDateRange = (
  startDate: string,
  endDate: string,
  isCurrent: number = 0,
  locale: string = "en-US",
): string => {
  const start = formatDateForLocale(startDate, locale);
  const end = isCurrent ? (locale === "ar" ? "حتى الآن" : "Present") : formatDateForLocale(endDate, locale);
  return `${start} - ${end}`;
};
