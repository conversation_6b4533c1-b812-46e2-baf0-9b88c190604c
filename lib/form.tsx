import { Input } from "@heroui/react";
import React, { useCallback, useMemo } from "react";

import { CheckboxField, DatePickerComponent, TrixEditorField } from "@/components/forms";
import { FullResume } from "@/db/schema";
import { useFormStore } from "@/lib/form-store";

// Stable FormField component to prevent focus loss
const FormField = React.memo(
  <T extends Record<string, any>>({
    property: fieldProperty,
    schemaProperty,
    index,
    item,
    schema,
    resumeData,
    schemaEntity,
  }: {
    property: string;
    schemaProperty: any;
    index: number;
    item: T;
    schema: string;
    resumeData?: any;
    schemaEntity?: string;
  }) => {
    const { updateNestedField, formData } = useFormStore();
    const { type, label, placeholder, className, description } = schemaProperty;

    // Memoize the current value to prevent infinite rerenders
    const currentValue = useMemo(() => {
      if (formData && formData[schema as keyof typeof formData]) {
        const collection = formData[schema as keyof typeof formData] as unknown as T[];
        const currentItem = collection[index];
        return currentItem?.[fieldProperty] || "";
      }
      return item?.[fieldProperty] || "";
    }, [formData, schema, index, fieldProperty, item]);

    // Memoize the update function to prevent unnecessary re-renders
    const handleValueChange = useCallback(
      (value: string) => {
        updateNestedField(`${schema}[${index}][${fieldProperty}]`, value);
      },
      [updateNestedField, schema, index, fieldProperty],
    );

    // Separate handler for boolean values (checkbox)
    const handleBooleanChange = useCallback(
      (value: boolean) => {
        updateNestedField(`${schema}[${index}][${fieldProperty}]`, value);
      },
      [updateNestedField, schema, index, fieldProperty],
    );

    switch (type) {
      case "string":
        return (
          <Input
            {...(label ? { label } : {})}
            {...(description ? { description } : {})}
            className={className}
            name={fieldProperty}
            placeholder={placeholder}
            value={currentValue}
            variant="bordered"
            onValueChange={handleValueChange}
          />
        );
      case "number":
        return (
          <Input
            {...(label ? { label } : {})}
            {...(description ? { description } : {})}
            className={className}
            name={fieldProperty}
            placeholder={placeholder}
            type="number"
            value={currentValue}
            variant="bordered"
            onValueChange={handleValueChange}
          />
        );
      case "date":
        return (
          <DatePickerComponent
            {...(label ? { label } : {})}
            {...(description ? { description } : {})}
            className={className}
            defaultValue={currentValue}
            name={fieldProperty}
            onChange={handleValueChange}
          />
        );
      case "boolean":
        return (
          <div className="flex items-center space-x-2 col-span-full">
            <CheckboxField defaultSelected={currentValue} name={fieldProperty} onChange={handleBooleanChange}>
              {label}
            </CheckboxField>
          </div>
        );
      case "textarea":
        return (
          <TrixEditorField
            {...(description ? { description } : {})}
            className="col-span-full"
            id={`${schema}_${index}_${fieldProperty}`}
            label={label}
            name={fieldProperty}
            value={currentValue}
            onChange={handleValueChange}
            enableAI={true}
            resumeContext={resumeData}
            itemContext={item}
            schemaEntity={schemaEntity}
          />
        );
      default:
        return null;
    }
  },
);

export const renderField = <T extends Record<string, any>>(
  property: string,
  schemaProperty: any,
  index: number,
  item: T,
  schema: string,
  resumeData?: any,
  schemaEntity?: string,
) => {
  return (
    <FormField
      property={property}
      schemaProperty={schemaProperty}
      index={index}
      item={item}
      schema={schema}
      resumeData={resumeData}
      schemaEntity={schemaEntity}
    />
  );
};

export const RenderForm = ({
  schema,
  item,
  index,
  resumeData,
}: {
  schema: any;
  item: any;
  index: number;
  resumeData?: any;
}) => {
  // Memoize the form fields to prevent unnecessary re-renders
  const formFields = useMemo(() => {
    return Object.entries(schema.properties).map(([property, schemaProperty]) => (
      <div
        key={`${schema.collection}-${index}-${property}`}
        className={(schemaProperty as any).type === "textarea" ? "col-span-full w-full" : ""}
      >
        {renderField(property, schemaProperty, index, item, schema.collection, resumeData, schema.entity)}
      </div>
    ));
  }, [schema, item, index, resumeData]);

  return <div className="grid grid-cols-1 w-full md:grid-cols-2 gap-4">{formFields}</div>;
};

export const prepareNestedForms = (resume?: FullResume, schemas?: any) => {
  return [
    {
      name: "profile",
      keyName: "network",
      schema: schemas.profilesSchema,
      items: resume?.profiles,
      properties: Object.keys(schemas.profilesSchema.properties),
    },
    {
      name: "education",
      keyName: "institution",
      schema: schemas.educationSchema,
      items: resume?.educations,
      properties: Object.keys(schemas.educationSchema.properties),
    },
    {
      name: "experience",
      keyName: "company",
      schema: schemas.experienceSchema,
      items: resume?.experiences,
      properties: Object.keys(schemas.experienceSchema.properties),
    },
    {
      name: "project",
      keyName: "title",
      schema: schemas.projectSchema,
      items: resume?.projects,
      properties: Object.keys(schemas.projectSchema.properties),
    },
    {
      name: "award",
      keyName: "title",
      schema: schemas.awardSchema,
      items: resume?.awards,
      properties: Object.keys(schemas.awardSchema.properties),
    },
    {
      name: "certification",
      keyName: "title",
      schema: schemas.certificationSchema,
      items: resume?.certifications,
      properties: Object.keys(schemas.certificationSchema.properties),
    },
    {
      name: "skill",
      keyName: "name",
      schema: schemas.skillSchema,
      items: resume?.skills,
      properties: Object.keys(schemas.skillSchema.properties),
    },
    {
      name: "language",
      keyName: "name",
      schema: schemas.languageSchema,
      items: resume?.languages,
      layout: "simple",
      properties: Object.keys(schemas.languageSchema.properties),
    },
    {
      name: "reference",
      keyName: "name",
      schema: schemas.referenceSchema,
      items: resume?.references,
      properties: Object.keys(schemas.referenceSchema.properties),
    },
    {
      name: "hobby",
      keyName: "name",
      schema: schemas.hobbySchema,
      items: resume?.hobbies,
      layout: "simple",
      properties: Object.keys(schemas.hobbySchema.properties),
    },
    {
      name: "volunteering",
      keyName: "role",
      schema: schemas.volunteeringSchema,
      items: resume?.volunteerings,
      properties: Object.keys(schemas.volunteeringSchema.properties),
    },
  ];
};
