import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";
import { FullResume } from "@/db/schema";

interface FormStore {
  formData: FullResume | null;
  setFormData: (data: FullResume) => void;
  updateField: (fieldName: keyof FullResume, value: unknown) => void;
  updateNestedField: (path: string, value: unknown) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  resetForm: () => void;
}

export const useFormStore = create<FormStore>()(
  subscribeWithSelector((set, get) => ({
    formData: null,

    setFormData: (data) => set({ formData: data }),

    updateField: (fieldName, value) =>
      set((state) => {
        if (!state.formData) return state;
        return {
          formData: { ...state.formData, [fieldName]: value },
        };
      }),

    updateNestedField: (path, value) => {
      set((state) => {
        if (!state.formData) return state;

        // Create a deep copy of the entire formData to ensure proper reactivity
        const newFormData = JSON.parse(JSON.stringify(state.formData)) as Record<string, unknown>;

        // Handle nested paths like "educations[0][city]"
        if (path.includes("[") && path.includes("]")) {
          // Parse the path: educations[0][city] -> ["educations", "0", "city"]
          const pathParts = path.replace(/\[/g, ".").replace(/\]/g, "").split(".");

          let current: Record<string, unknown> = newFormData;

          // Navigate to the parent object/array
          for (let i = 0; i < pathParts.length - 1; i++) {
            const part = pathParts[i];
            const isArrayIndex = /^\d+$/.test(part);

            if (isArrayIndex) {
              const index = parseInt(part);
              if (!Array.isArray(current)) {
                console.warn(`Expected array at ${pathParts.slice(0, i).join(".")}, but found:`, current);
                return { formData: state.formData }; // Return unchanged state
              }
              if (!(current as unknown[])[index]) {
                (current as unknown[])[index] = {};
              }
              current = (current as unknown[])[index] as Record<string, unknown>;
            } else {
              if (!current[part]) {
                // Determine if next part is an array index to create array or object
                const nextPart = pathParts[i + 1];
                const nextIsArrayIndex = /^\d+$/.test(nextPart);
                current[part] = nextIsArrayIndex ? [] : {};
              }
              current = current[part] as Record<string, unknown>;
            }
          }

          // Set the final value
          const finalKey = pathParts[pathParts.length - 1];
          current[finalKey] = value;
        } else {
          // Simple field update for non-nested fields
          newFormData[path] = value;
        }

        return { formData: newFormData as unknown as FullResume };
      });
    },

    handleInputChange: (e) => {
      const { name, value, type } = e.target;
      const checked = (e.target as HTMLInputElement).checked;
      const newValue = type === "checkbox" ? checked : value;

      // Extract field name from complex nested name patterns
      const fieldName = name.split("][").pop()?.replace("]", "") || name;

      get().updateField(fieldName as keyof FullResume, newValue);
    },

    resetForm: () => set({ formData: null }),
  })),
);
