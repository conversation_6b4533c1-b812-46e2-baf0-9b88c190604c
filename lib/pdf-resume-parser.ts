import type { ResumeSection } from "@/types/resume";

export interface ParsedResumeData {
  personalInfo: {
    firstName?: string;
    lastName?: string;
    fullName?: string;
    jobTitle?: string;
    email?: string;
    phone?: string;
    address?: string;
    city?: string;
    country?: string;
    website?: string;
    linkedin?: string;
    github?: string;
    twitter?: string;
    portfolio?: string;
  };
  summary?: string;
  experiences: Array<{
    title: string;
    company: string;
    location?: string;
    city?: string;
    country?: string;
    startDate?: string;
    endDate?: string;
    isCurrent?: boolean;
    description: string;
  }>;
  educations: Array<{
    degree: string;
    institution: string;
    fieldOfStudy?: string;
    location?: string;
    city?: string;
    country?: string;
    startDate?: string;
    endDate?: string;
    gpa?: string;
    isCurrent?: boolean;
  }>;
  skills: Array<{
    name: string;
    category?: string;
  }>;
  projects: Array<{
    name: string;
    description: string;
    technologies?: string[];
    url?: string;
    startDate?: string;
    endDate?: string;
  }>;
  languages: Array<{
    name: string;
    proficiency?: string;
  }>;
  certifications: Array<{
    name: string;
    issuer?: string;
    issueDate?: string;
    expiryDate?: string;
    url?: string;
    description?: string;
  }>;
  awards: Array<{
    name: string;
    issuer?: string;
    dateReceived?: string;
    description?: string;
  }>;
  volunteering: Array<{
    organization: string;
    role?: string;
    startDate?: string;
    endDate?: string;
    description?: string;
    isCurrent?: boolean;
  }>;
  references: Array<{
    name: string;
    title?: string;
    company?: string;
    email?: string;
    phone?: string;
    relationship?: string;
  }>;
  hobbies: Array<{
    name: string;
    description?: string;
  }>;
  profiles: Array<{
    platform: string;
    url: string;
    username?: string;
  }>;
  // Quality metrics
  confidence: {
    overall: number;
    personalInfo: number;
    experiences: number;
    educations: number;
    skills: number;
  };
}

class SectionParser {
  private readonly sectionKeywords = {
    experience: ["experience", "work experience", "professional experience", "employment", "employment history", "career history", "work history"],
    education: ["education", "academic background", "qualifications", "academic qualifications", "academic history"],
    skills: ["skills", "technical skills", "core competencies", "expertise", "competencies", "technologies", "technical expertise"],
    projects: ["projects", "personal projects", "key projects", "project experience", "portfolio", "selected projects"],
    summary: ["summary", "objective", "profile", "about", "professional summary", "career objective", "professional profile"],
    certifications: ["certifications", "certificates", "licenses", "professional certifications", "credentials"],
    languages: ["languages", "language skills", "spoken languages", "language proficiency"],
    awards: ["awards", "honors", "achievements", "recognition", "accomplishments", "honors and awards"],
    volunteering: ["volunteer", "volunteering", "volunteer experience", "community service", "volunteer work", "community involvement"],
    references: ["references", "professional references", "referees", "contacts"],
    hobbies: ["hobbies", "interests", "personal interests", "activities", "hobbies and interests"],
  };

  identifySections(lines: string[]): Record<string, string[]> {
    const sections: Record<string, string[]> = {};
    let currentSection = "personalInfo";
    sections[currentSection] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lowerLine = line.toLowerCase();
      const sectionName = this.getSectionName(lowerLine);

      if (sectionName) {
        currentSection = sectionName;
        if (!sections[currentSection]) {
          sections[currentSection] = [];
        }
        // Skip the section header line itself
        continue;
      } else if (sections[currentSection]) {
        sections[currentSection].push(line);
      }
    }

    return sections;
  }

  private getSectionName(line: string): string | null {
    for (const [section, keywords] of Object.entries(this.sectionKeywords)) {
      if (keywords.some(keyword => line.includes(keyword) && line.length < 50)) {
        return section;
      }
    }
    return null;
  }
}

class PersonalInfoParser {
  parse(lines: string[]): ParsedResumeData["personalInfo"] {
    const personalInfo: ParsedResumeData["personalInfo"] = {};
    const topLines = lines.slice(0, 15); // Look at more lines for better extraction

    // Extract information in order of priority
    for (const line of topLines) {
      this.extractEmail(line, personalInfo);
      this.extractPhone(line, personalInfo);
      this.extractSocialProfiles(line, personalInfo);
      this.extractWebsite(line, personalInfo);
      this.extractAddress(line, personalInfo);
      this.extractJobTitle(line, personalInfo);
    }

    // Extract name last to avoid conflicts with other fields
    for (const line of topLines) {
      this.extractName(line, personalInfo);
      if (personalInfo.fullName) break; // Stop once we find a name
    }

    // Split full name into first and last name if we have it
    if (personalInfo.fullName && !personalInfo.firstName && !personalInfo.lastName) {
      const nameParts = personalInfo.fullName.trim().split(/\s+/);
      personalInfo.firstName = nameParts[0] || "";
      personalInfo.lastName = nameParts.slice(1).join(" ") || "";
    }

    return personalInfo;
  }

  private extractEmail(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    const emailMatch = line.match(/\b[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}\b/);
    if (emailMatch && !personalInfo.email) {
      personalInfo.email = emailMatch[0].toLowerCase();
    }
  }

  private extractPhone(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    const phoneMatch = line.match(/(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/);
    if (phoneMatch && !personalInfo.phone) {
      personalInfo.phone = phoneMatch[0];
    }
  }

  private extractLinkedIn(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    const linkedinMatch = line.match(/linkedin\.com\/in\/[\w-]+/);
    if (linkedinMatch && !personalInfo.linkedin) {
      personalInfo.linkedin = linkedinMatch[0];
    }
  }

  private extractWebsite(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    const websiteMatch = line.match(/(?:https?:\/\/)?(?:www\.)?[\w.-]+\.\w+(?:\/[\w.-]*)*(?:\?[\w=&.-]*)?/);
    if (websiteMatch && !websiteMatch[0].includes("linkedin") && !websiteMatch[0].includes("@") && !personalInfo.website) {
      personalInfo.website = websiteMatch[0];
    }
  }

  private extractSocialProfiles(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    // GitHub
    const githubMatch = line.match(/(?:https?:\/\/)?(?:www\.)?github\.com\/[\w-]+/i);
    if (githubMatch && !personalInfo.github) {
      personalInfo.github = githubMatch[0];
    }

    // Twitter
    const twitterMatch = line.match(/(?:https?:\/\/)?(?:www\.)?(?:twitter\.com|x\.com)\/[\w-]+/i);
    if (twitterMatch && !personalInfo.twitter) {
      personalInfo.twitter = twitterMatch[0];
    }

    // Portfolio (common patterns)
    const portfolioPatterns = [
      /(?:portfolio|website):\s*(https?:\/\/[\w.-]+)/i,
      /(?:https?:\/\/)?[\w-]+\.(?:dev|io|me|com|net|org)(?:\/[\w.-]*)?/
    ];

    for (const pattern of portfolioPatterns) {
      const portfolioMatch = line.match(pattern);
      if (portfolioMatch && !personalInfo.portfolio &&
          !portfolioMatch[0].includes('linkedin') &&
          !portfolioMatch[0].includes('github') &&
          !portfolioMatch[0].includes('twitter')) {
        personalInfo.portfolio = portfolioMatch[0];
        break;
      }
    }
  }

  private extractAddress(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    // Look for address patterns
    const addressPatterns = [
      /\d+\s+[\w\s]+(?:street|st|avenue|ave|road|rd|drive|dr|lane|ln|boulevard|blvd)/i,
      /(?:address|location):\s*(.+)/i,
    ];

    for (const pattern of addressPatterns) {
      const addressMatch = line.match(pattern);
      if (addressMatch && !personalInfo.address) {
        personalInfo.address = addressMatch[1] || addressMatch[0];
        break;
      }
    }

    // Extract city and country from location-like patterns
    const locationPatterns = [
      /([\w\s]+),\s*([A-Z]{2,3}|[\w\s]+)$/,  // City, State/Country
      /([\w\s]+),\s*([\w\s]+),\s*([\w\s]+)$/, // City, State, Country
    ];

    for (const pattern of locationPatterns) {
      const locationMatch = line.match(pattern);
      if (locationMatch && !personalInfo.city && line.length < 100) {
        if (locationMatch.length === 3) {
          personalInfo.city = locationMatch[1].trim();
          personalInfo.country = locationMatch[2].trim();
        } else if (locationMatch.length === 4) {
          personalInfo.city = locationMatch[1].trim();
          personalInfo.country = locationMatch[3].trim();
        }
        break;
      }
    }
  }

  private extractJobTitle(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    // Common job title patterns
    const jobTitlePatterns = [
      /\b(senior|junior|lead|principal|staff|associate)\s+(engineer|developer|designer|manager|analyst|consultant)/i,
      /\b(software|web|mobile|frontend|backend|fullstack|full-stack)\s+(engineer|developer)/i,
      /\b(product|project|engineering|technical)\s+manager/i,
      /\b(data|business|systems|security)\s+analyst/i,
      /\b(ux|ui)\s+designer/i,
      /\b(devops|sre|site reliability)\s+engineer/i,
    ];

    for (const pattern of jobTitlePatterns) {
      const titleMatch = line.match(pattern);
      if (titleMatch && !personalInfo.jobTitle && line.length < 100 &&
          !line.toLowerCase().includes('experience') &&
          !line.toLowerCase().includes('education')) {
        personalInfo.jobTitle = titleMatch[0];
        break;
      }
    }
  }

  private extractName(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    // Skip if we already have a name or if line contains other info
    if (personalInfo.fullName ||
        personalInfo.email ||
        personalInfo.phone ||
        personalInfo.linkedin ||
        personalInfo.website ||
        line.includes('@') ||
        line.includes('http') ||
        /\d/.test(line) ||
        line.length > 50 ||
        line.length < 4) {
      return;
    }

    // Check if line looks like a name (2-4 words, proper case, no special chars)
    const words = line.trim().split(/\s+/);
    if (words.length >= 2 && words.length <= 4) {
      const isValidName = words.every(word =>
        /^[A-Za-z][a-z]*$/.test(word) || // Proper case
        /^[A-Z]+$/.test(word) || // All caps (initials)
        word.length === 1 // Single letter (middle initial)
      );

      if (isValidName &&
          !/^(address|location|city|phone|email|summary|objective|experience|education|skills|profile)/i.test(line)) {
        personalInfo.fullName = line.trim();
      }
    }
  }
}

class ExperienceParser {
  parse(lines: string[]): ParsedResumeData["experiences"] {
    const experiences: ParsedResumeData["experiences"] = [];
    let currentExperience: Partial<ParsedResumeData["experiences"][0]> | null = null;
    let descriptionLines: string[] = [];

    const commitExperience = () => {
      if (currentExperience) {
        currentExperience.description = descriptionLines.join(' ').trim();
        // Basic validation to ensure we have at least a title or company
        if (currentExperience.title || currentExperience.company) {
          experiences.push(currentExperience as ParsedResumeData["experiences"][0]);
        }
        currentExperience = null;
        descriptionLines = [];
      }
    };

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (this.isDateRangeLine(line)) {
            commitExperience();
            currentExperience = this.parseDateRange(line);

            // The next lines are often location, title, company.
            // We look ahead a few lines to find them.
            let consumed = 0;
            const lookahead = lines.slice(i + 1, i + 4);

            // Check for location (e.g., "Remote", "Freelance")
            if (lookahead[0] && (lookahead[0].toLowerCase().includes('remote') || lookahead[0].toLowerCase().includes('freelance'))) {
                currentExperience.location = lookahead[0];
                consumed++;
            }

            // Check for job title
            if (lookahead[consumed] && this.looksLikeJobTitle(lookahead[consumed])) {
                currentExperience.title = lookahead[consumed];
                consumed++;
            }

            // Assume the next line is the company
            if (lookahead[consumed] && !this.isDateRangeLine(lookahead[consumed])) {
                 currentExperience.company = lookahead[consumed];
                 consumed++;
            }

            i += consumed; // Advance the loop index by the number of lines we consumed

        } else if (currentExperience) {
            // If we are in the middle of an experience entry, this line is part of the description.
            descriptionLines.push(line);
        }
    }

    commitExperience(); // Commit the very last experience entry
    return experiences;
  }

  private isDateRangeLine(line: string): boolean {
    // Checks for a date pattern like MM/YYYY, Month YYYY, or YYYY, followed by a dash.
    return /(\d{1,2}\/\d{4}|\b(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b\s+\d{4}|\d{4})\s*[-–]/.test(line);
  }

  private looksLikeJobTitle(line: string): boolean {
    const jobTitlePatterns = [/\b(engineer|developer|manager|analyst|specialist|coordinator|director|lead)\b/i, /\b(senior|junior|principal|staff|associate)\b/i];
    // Ensure it's not a description line by checking length and common description starters
    return jobTitlePatterns.some(pattern => pattern.test(line)) && line.length < 100 && !line.startsWith('•') && !line.startsWith('-');
  }

  private parseDateRange(line: string): { startDate?: string; endDate?: string } {
    const rangeMatch = line.match(/(\d{1,2}\/\d{4}|\w+\s+\d{4}|\d{4})\s*[-–]\s*(\d{1,2}\/\d{4}|\w+\s+\d{4}|\d{4}|present)/i);
    if (rangeMatch) {
      const start = rangeMatch[1];
      const end = rangeMatch[2];
      return {
        startDate: this.normalizeDate(start),
        endDate: end.toLowerCase() === 'present' ? 'Present' : this.normalizeDate(end)
      };
    }
    return {};
  }

  private normalizeDate(dateStr: string): string {
    // Handle MM/YYYY format specifically
    if (/^\d{1,2}\/\d{4}$/.test(dateStr)) {
        const [month, year] = dateStr.split('/');
        return `${year}-${month.padStart(2, '0')}`;
    }
    // Handle other common date formats
    const date = new Date(dateStr);
    return !isNaN(date.getTime()) ? date.toISOString().split("T")[0] : dateStr;
  }
}

class EducationParser {
  parse(lines: string[]): ParsedResumeData["educations"] {
    const educations: ParsedResumeData["educations"] = [];
    let currentEducation: Partial<ParsedResumeData["educations"][0]> = {};

    for (const line of lines) {
      if (this.looksLikeDegree(line)) {
        if (currentEducation.degree || currentEducation.institution) {
          educations.push(currentEducation as ParsedResumeData["educations"][0]);
        }
        currentEducation = this.parseDegreeLine(line);
      } else if (this.looksLikeDateRange(line)) {
        const dates = this.parseDateRange(line);
        currentEducation.startDate = dates.start;
        currentEducation.endDate = dates.end;
      } else if (line.toLowerCase().includes("gpa")) {
        const gpaMatch = line.match(/(\d+\.\d+)/);
        if (gpaMatch) {
          currentEducation.gpa = gpaMatch[1];
        }
      }
    }

    if (currentEducation.degree || currentEducation.institution) {
      educations.push(currentEducation as ParsedResumeData["educations"][0]);
    }

    return educations;
  }

  private looksLikeDegree(line: string): boolean {
    const degreePatterns = [/\b(bachelor|master|phd|doctorate|associate|diploma|certificate)\b/i, /\b(bs|ba|ms|ma|mba|phd|md|jd)\b/i, /\bin\s+/i];
    return degreePatterns.some(pattern => pattern.test(line));
  }

  private looksLikeDateRange(line: string): boolean {
    return /\d{4}|\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b|\bpresent\b/i.test(line);
  }

  private parseDegreeLine(line: string): Partial<ParsedResumeData["educations"][0]> {
    // Pattern: "Bachelor of Science in Computer Science from MIT"
    const fullMatch = line.match(/^(.*?)\s+in\s+(.*?)(?:\s+(?:from|at)\s+(.+))?$/i);
    if (fullMatch) {
      return {
        degree: fullMatch[1].trim(),
        fieldOfStudy: fullMatch[2].trim(),
        institution: fullMatch[3]?.trim() || ""
      };
    }

    // Pattern: "Computer Science, Bachelor of Science"
    const reverseMatch = line.match(/^(.*?),\s+(.*?)(?:\s+(?:from|at)\s+(.+))?$/i);
    if (reverseMatch && this.looksLikeDegreeType(reverseMatch[2])) {
      return {
        fieldOfStudy: reverseMatch[1].trim(),
        degree: reverseMatch[2].trim(),
        institution: reverseMatch[3]?.trim() || ""
      };
    }

    // Pattern: "Bachelor's Degree in Engineering"
    const degreeInMatch = line.match(/^(.*?)\s+(?:degree\s+)?in\s+(.*?)$/i);
    if (degreeInMatch) {
      return {
        degree: degreeInMatch[1].trim(),
        fieldOfStudy: degreeInMatch[2].trim()
      };
    }

    return { degree: line };
  }

  private looksLikeDegreeType(text: string): boolean {
    const degreeTypes = ["bachelor", "master", "phd", "doctorate", "associate", "diploma", "certificate", "bs", "ba", "ms", "ma", "mba", "phd", "md", "jd"];
    return degreeTypes.some(type => text.toLowerCase().includes(type));
  }

  private parseDateRange(line: string): { start?: string; end?: string } {
    const rangeMatch = line.match(/(\w+\s+\d{4}|\d{4})\s*[-–]\s*(\w+\s+\d{4}|\d{4}|present)/i);
    if (rangeMatch) {
      return { start: this.normalizeDate(rangeMatch[1]), end: rangeMatch[2].toLowerCase() === "present" ? "" : this.normalizeDate(rangeMatch[2]) };
    }
    return {};
  }

  private normalizeDate(dateStr: string): string {
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? dateStr : date.toISOString().split("T")[0];
  }
}

class SkillsParser {
  parse(lines: string[]): ParsedResumeData["skills"] {
    const skills: ParsedResumeData["skills"] = [];
    const skillCategories = {
      programming: ["javascript", "python", "java", "c++", "c#", "php", "ruby", "go", "rust", "swift", "kotlin", "typescript"],
      frameworks: ["react", "angular", "vue", "node", "express", "django", "flask", "spring", "laravel", "rails"],
      databases: ["mysql", "postgresql", "mongodb", "redis", "sqlite", "oracle", "sql server"],
      cloud: ["aws", "azure", "gcp", "docker", "kubernetes", "terraform", "jenkins"],
      tools: ["git", "jira", "confluence", "slack", "trello", "figma", "sketch", "photoshop"],
      soft: ["leadership", "communication", "teamwork", "problem solving", "project management"],
    };

    for (const line of lines) {
      // Replace '#' with a comma, then split by common delimiters.
      // This handles "#skill1#skill2" and "skill1, skill2".
      const skillItems = line.replace(/#/g, ',').split(/[,•·|]/).map(s => s.trim()).filter(s => s.length > 0);

      for (const skillItem of skillItems) {
        // Filter out non-skill-like items
        if (skillItem.length > 1 && skillItem.length < 35 &&
            !skillItem.match(/^\d+$/) &&
            !skillItem.toLowerCase().includes("years")) {

          const category = this.categorizeSkill(skillItem, skillCategories);
          skills.push({
            name: skillItem,
            category,
          });
        }
      }
    }

    return skills;
  }

  private categorizeSkill(skill: string, categories: Record<string, string[]>): string | undefined {
    const lowerSkill = skill.toLowerCase();

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => lowerSkill.includes(keyword))) {
        return category;
      }
    }

    return undefined;
  }
}

class ProjectsParser {
  parse(lines: string[]): ParsedResumeData["projects"] {
    const projects: ParsedResumeData["projects"] = [];
    let currentProject: Partial<ParsedResumeData["projects"][0]> = {};
    let descriptionLines: string[] = [];

    for (const line of lines) {
      if (this.looksLikeProjectTitle(line)) {
        if (currentProject.name) {
          currentProject.description = descriptionLines.join(" ");
          projects.push(currentProject as ParsedResumeData["projects"][0]);
        }
        currentProject = { name: line };
        descriptionLines = [];
      } else if (line.length > 10) {
        descriptionLines.push(line);
      }
    }

    if (currentProject.name) {
      currentProject.description = descriptionLines.join(" ");
      projects.push(currentProject as ParsedResumeData["projects"][0]);
    }

    return projects;
  }

  private looksLikeProjectTitle(line: string): boolean {
    return line.length < 80 && !line.includes("•") && !line.includes("-");
  }
}

class SummaryParser {
  parse(lines: string[]): string {
    return lines.join(" ");
  }
}

class CertificationsParser {
  parse(lines: string[]): ParsedResumeData["certifications"] {
    const certifications: ParsedResumeData["certifications"] = [];
    let currentCert: Partial<ParsedResumeData["certifications"][0]> = {};
    let descriptionLines: string[] = [];

    for (const line of lines) {
      if (this.looksLikeCertificationTitle(line)) {
        if (currentCert.name) {
          currentCert.description = descriptionLines.join(" ");
          certifications.push(currentCert as ParsedResumeData["certifications"][0]);
        }
        currentCert = this.parseCertificationLine(line);
        descriptionLines = [];
      } else if (this.looksLikeDateRange(line)) {
        const dates = this.parseDateRange(line);
        currentCert.issueDate = dates.start;
        currentCert.expiryDate = dates.end;
      } else if (line.toLowerCase().includes("issued by") || line.toLowerCase().includes("issuer")) {
        const issuerMatch = line.match(/(?:issued by|issuer):?\s*(.+)/i);
        if (issuerMatch) {
          currentCert.issuer = issuerMatch[1].trim();
        }
      } else if (line.length > 10) {
        descriptionLines.push(line);
      }
    }

    if (currentCert.name) {
      currentCert.description = descriptionLines.join(" ");
      certifications.push(currentCert as ParsedResumeData["certifications"][0]);
    }

    return certifications;
  }

  private looksLikeCertificationTitle(line: string): boolean {
    return this.looksLikeCertification(line) && line.length < 100 && !line.includes("•") && !line.includes("-");
  }

  private looksLikeCertification(line: string): boolean {
    const certificationPatterns = [/\b(certified|certification|certificate|license|licensed)\b/i, /\b(aws|azure|google cloud|gcp|microsoft|oracle|cisco|comptia)\b/i, /\b(pmp|cissp|ceh|ccna|mcse|rhce|cka|ckad)\b/i, /\b(issued by|expires|valid until)\b/i];
    return certificationPatterns.some(pattern => pattern.test(line));
  }

  private parseCertificationLine(line: string): Partial<ParsedResumeData["certifications"][0]> {
    const fromMatch = line.match(/^(.*?)\s+(?:from|by)\s+(.+)$/i);
    if (fromMatch) {
      return { name: fromMatch[1].trim(), issuer: fromMatch[2].trim() };
    }
    return { name: line.trim() };
  }

  private looksLikeDateRange(line: string): boolean {
    return /\d{4}|\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b|\bpresent\b/i.test(line);
  }

  private parseDateRange(line: string): { start?: string; end?: string } {
    const rangeMatch = line.match(/(\w+\s+\d{4}|\d{4})\s*[-–]\s*(\w+\s+\d{4}|\d{4}|present)/i);
    if (rangeMatch) {
      return { start: this.normalizeDate(rangeMatch[1]), end: rangeMatch[2].toLowerCase() === "present" ? "" : this.normalizeDate(rangeMatch[2]) };
    }
    return {};
  }

  private normalizeDate(dateStr: string): string {
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? dateStr : date.toISOString().split("T")[0];
  }
}

class LanguagesParser {
  parse(lines: string[]): ParsedResumeData["languages"] {
    const languages: ParsedResumeData["languages"] = [];
    const proficiencyLevels = ['native', 'fluent', 'proficient', 'conversational', 'basic'];

    for (const line of lines) {
      const items = line.split(/[,|]/).map(item => item.trim());

      for (const item of items) {
        let languageName = item;
        let proficiency: string | undefined;

        const proficiencyMatch = item.match(/\((.*?)\)|:\s*(.*)/);
        if (proficiencyMatch) {
          const potentialProficiency = (proficiencyMatch[1] || proficiencyMatch[2] || '').toLowerCase().trim();
          if (proficiencyLevels.includes(potentialProficiency)) {
            proficiency = potentialProficiency;
            languageName = item.replace(proficiencyMatch[0], '').trim();
          }
        }

        if (languageName) {
            languages.push({ name: languageName, proficiency });
        }
      }
    }
    return languages;
  }
}

class AwardsParser {
  parse(lines: string[]): ParsedResumeData["awards"] {
    const awards: ParsedResumeData["awards"] = [];
    let currentAward: Partial<ParsedResumeData["awards"][0]> = {};
    let descriptionLines: string[] = [];

    for (const line of lines) {
      if (this.looksLikeAwardTitle(line)) {
        if (currentAward.name) {
          currentAward.description = descriptionLines.join(" ");
          awards.push(currentAward as ParsedResumeData["awards"][0]);
        }
        currentAward = this.parseAwardLine(line);
        descriptionLines = [];
      } else if (this.looksLikeDateRange(line)) {
        const dateMatch = line.match(/(\w+\s+\d{4}|\d{4})/i);
        if (dateMatch) {
          currentAward.dateReceived = this.normalizeDate(dateMatch[1]);
        }
      } else if (line.toLowerCase().includes("issued by") || line.toLowerCase().includes("issuer")) {
        const issuerMatch = line.match(/(?:issued by|issuer):?\s*(.+)/i);
        if (issuerMatch) {
          currentAward.issuer = issuerMatch[1].trim();
        }
      } else if (line.length > 10) {
        descriptionLines.push(line);
      }
    }

    if (currentAward.name) {
      currentAward.description = descriptionLines.join(" ");
      awards.push(currentAward as ParsedResumeData["awards"][0]);
    }

    return awards;
  }

  private looksLikeAwardTitle(line: string): boolean {
    const awardPatterns = [
      /\b(award|honor|recognition|achievement|medal|prize|scholarship)\b/i,
      /\b(dean's list|magna cum laude|summa cum laude|cum laude)\b/i,
      /\b(employee of the|outstanding|excellence|best)\b/i,
    ];
    return awardPatterns.some(pattern => pattern.test(line)) && line.length < 100;
  }

  private parseAwardLine(line: string): Partial<ParsedResumeData["awards"][0]> {
    const fromMatch = line.match(/^(.*?)\s+(?:from|by)\s+(.+)$/i);
    if (fromMatch) {
      return { name: fromMatch[1].trim(), issuer: fromMatch[2].trim() };
    }
    return { name: line.trim() };
  }

  private looksLikeDateRange(line: string): boolean {
    return /\d{4}|\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b/i.test(line);
  }

  private normalizeDate(dateStr: string): string {
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? dateStr : date.toISOString().split("T")[0];
  }
}

class VolunteeringParser {
  parse(lines: string[]): ParsedResumeData["volunteering"] {
    const volunteering: ParsedResumeData["volunteering"] = [];
    let currentVolunteering: Partial<ParsedResumeData["volunteering"][0]> = {};
    let descriptionLines: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (this.isDateRangeLine(line)) {
        if (currentVolunteering.organization) {
          currentVolunteering.description = descriptionLines.join(" ").trim();
          volunteering.push(currentVolunteering as ParsedResumeData["volunteering"][0]);
        }
        const dates = this.parseDateRange(line);
        currentVolunteering = {
          startDate: dates.start,
          endDate: dates.end,
          isCurrent: dates.end?.toLowerCase() === "present",
        };
        descriptionLines = [];

        // Look ahead for organization and role
        const lookahead = lines.slice(i + 1, i + 3);
        let consumed = 0;

        if (lookahead[0] && this.looksLikeRole(lookahead[0])) {
          currentVolunteering.role = lookahead[0];
          consumed++;
        }

        if (lookahead[consumed] && !this.isDateRangeLine(lookahead[consumed])) {
          currentVolunteering.organization = lookahead[consumed];
          consumed++;
        }

        i += consumed;
      } else if (currentVolunteering.organization || currentVolunteering.role) {
        descriptionLines.push(line);
      }
    }

    if (currentVolunteering.organization) {
      currentVolunteering.description = descriptionLines.join(" ").trim();
      volunteering.push(currentVolunteering as ParsedResumeData["volunteering"][0]);
    }

    return volunteering;
  }

  private isDateRangeLine(line: string): boolean {
    return /(\d{1,2}\/\d{4}|\b(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b\s+\d{4}|\d{4})\s*[-–]/.test(line);
  }

  private looksLikeRole(line: string): boolean {
    const rolePatterns = [/\b(volunteer|coordinator|assistant|member|leader|organizer)\b/i];
    return rolePatterns.some(pattern => pattern.test(line)) && line.length < 80;
  }

  private parseDateRange(line: string): { start?: string; end?: string } {
    const rangeMatch = line.match(/(\d{1,2}\/\d{4}|\w+\s+\d{4}|\d{4})\s*[-–]\s*(\d{1,2}\/\d{4}|\w+\s+\d{4}|\d{4}|present)/i);
    if (rangeMatch) {
      return {
        start: this.normalizeDate(rangeMatch[1]),
        end: rangeMatch[2].toLowerCase() === "present" ? "Present" : this.normalizeDate(rangeMatch[2]),
      };
    }
    return {};
  }

  private normalizeDate(dateStr: string): string {
    if (/^\d{1,2}\/\d{4}$/.test(dateStr)) {
      const [month, year] = dateStr.split("/");
      return `${year}-${month.padStart(2, "0")}`;
    }
    const date = new Date(dateStr);
    return !isNaN(date.getTime()) ? date.toISOString().split("T")[0] : dateStr;
  }
}

class ReferencesParser {
  parse(lines: string[]): ParsedResumeData["references"] {
    const references: ParsedResumeData["references"] = [];
    let currentReference: Partial<ParsedResumeData["references"][0]> = {};

    for (const line of lines) {
      if (this.looksLikeName(line)) {
        if (currentReference.name) {
          references.push(currentReference as ParsedResumeData["references"][0]);
        }
        currentReference = { name: line.trim() };
      } else if (line.includes("@")) {
        const emailMatch = line.match(/\b[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}\b/);
        if (emailMatch) {
          currentReference.email = emailMatch[0];
        }
      } else if (this.looksLikePhone(line)) {
        const phoneMatch = line.match(/[\d\s.-()]+/);
        if (phoneMatch) {
          currentReference.phone = phoneMatch[0].trim();
        }
      } else if (this.looksLikeTitle(line)) {
        currentReference.title = line.trim();
      } else if (this.looksLikeCompany(line)) {
        currentReference.company = line.trim();
      }
    }

    if (currentReference.name) {
      references.push(currentReference as ParsedResumeData["references"][0]);
    }

    return references;
  }

  private looksLikeName(line: string): boolean {
    const words = line.trim().split(/\s+/);
    return words.length >= 2 && words.length <= 4 &&
           words.every(word => /^[A-Za-z][a-z]*$/.test(word)) &&
           line.length < 50;
  }

  private looksLikePhone(line: string): boolean {
    return /[\d\s.-()]{10,}/.test(line) && line.replace(/\D/g, "").length >= 10;
  }

  private looksLikeTitle(line: string): boolean {
    const titlePatterns = [/\b(manager|director|engineer|analyst|specialist|coordinator)\b/i];
    return titlePatterns.some(pattern => pattern.test(line)) && line.length < 80;
  }

  private looksLikeCompany(line: string): boolean {
    return line.length > 5 && line.length < 80 && !line.includes("@") && !/\d{10}/.test(line);
  }
}

class HobbiesParser {
  parse(lines: string[]): ParsedResumeData["hobbies"] {
    const hobbies: ParsedResumeData["hobbies"] = [];

    for (const line of lines) {
      const items = line.split(/[,•·|]/).map(item => item.trim()).filter(item => item.length > 0);

      for (const item of items) {
        if (item.length > 2 && item.length < 50) {
          hobbies.push({ name: item });
        }
      }
    }

    return hobbies;
  }
}

export class PDFResumeParser {
  private sectionParser = new SectionParser();
  private personalInfoParser = new PersonalInfoParser();
  private experienceParser = new ExperienceParser();
  private educationParser = new EducationParser();
  private skillsParser = new SkillsParser();
  private projectsParser = new ProjectsParser();
  private summaryParser = new SummaryParser();
  private certificationsParser = new CertificationsParser();
  private languagesParser = new LanguagesParser();
  private awardsParser = new AwardsParser();
  private volunteeringParser = new VolunteeringParser();
  private referencesParser = new ReferencesParser();
  private hobbiesParser = new HobbiesParser();

  async parsePDFResume(pdfBuffer: Buffer): Promise<ParsedResumeData> {
    try {
      const pdfExtraction = (await import("pdf-extraction")).default;
      const extractedData = await pdfExtraction(pdfBuffer);
      const extractedText = extractedData.text;

      console.log("Extracted Text:", extractedText);

      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error("No text content found in PDF. The PDF might be image-based or corrupted.");
      }

      return this.parseResumeText(extractedText);
    } catch (error) {
      console.error("Error parsing PDF resume:", error);
      if (error instanceof Error) {
        throw new Error(`Failed to parse PDF resume: ${error.message}`);
      }
      throw new Error("Failed to parse PDF resume. Please ensure the file is a valid PDF.");
    }
  }

  private parseResumeText(text: string): ParsedResumeData {
    const lines = text.split("\n").map(line => line.trim()).filter(line => line.length > 0);
    const sections = this.sectionParser.identifySections(lines);

    // Parse all sections
    const personalInfo = this.personalInfoParser.parse(sections.personalInfo || []);
    const experiences = this.experienceParser.parse(sections.experience || []);
    const educations = this.educationParser.parse(sections.education || []);
    const skills = this.skillsParser.parse(sections.skills || []);
    const projects = this.projectsParser.parse(sections.projects || []);
    const summary = this.summaryParser.parse(sections.summary || []);
    const certifications = this.certificationsParser.parse(sections.certifications || []);
    const languages = this.languagesParser.parse(sections.languages || []);
    const awards = this.awardsParser.parse(sections.awards || []);
    const volunteering = this.volunteeringParser.parse(sections.volunteering || []);
    const references = this.referencesParser.parse(sections.references || []);
    const hobbies = this.hobbiesParser.parse(sections.hobbies || []);

    // Extract profiles from personal info
    const profiles = this.extractProfiles(personalInfo);

    // Calculate confidence scores
    const confidence = this.calculateConfidence({
      personalInfo,
      experiences,
      educations,
      skills,
    });

    return {
      personalInfo,
      experiences,
      educations,
      skills,
      projects,
      summary,
      certifications,
      languages,
      awards,
      volunteering,
      references,
      hobbies,
      profiles,
      confidence,
    };
  }

  private extractProfiles(personalInfo: ParsedResumeData["personalInfo"]): ParsedResumeData["profiles"] {
    const profiles: ParsedResumeData["profiles"] = [];

    if (personalInfo.linkedin) {
      profiles.push({
        platform: "LinkedIn",
        url: personalInfo.linkedin,
        username: personalInfo.linkedin.split("/").pop() || "",
      });
    }

    if (personalInfo.github) {
      profiles.push({
        platform: "GitHub",
        url: personalInfo.github,
        username: personalInfo.github.split("/").pop() || "",
      });
    }

    if (personalInfo.twitter) {
      profiles.push({
        platform: "Twitter",
        url: personalInfo.twitter,
        username: personalInfo.twitter.split("/").pop() || "",
      });
    }

    if (personalInfo.portfolio) {
      profiles.push({
        platform: "Portfolio",
        url: personalInfo.portfolio,
      });
    }

    return profiles;
  }

  private calculateConfidence(data: {
    personalInfo: ParsedResumeData["personalInfo"];
    experiences: ParsedResumeData["experiences"];
    educations: ParsedResumeData["educations"];
    skills: ParsedResumeData["skills"];
  }): ParsedResumeData["confidence"] {
    // Calculate confidence scores based on data completeness and quality
    const personalInfoScore = this.calculatePersonalInfoConfidence(data.personalInfo);
    const experiencesScore = this.calculateExperiencesConfidence(data.experiences);
    const educationsScore = this.calculateEducationsConfidence(data.educations);
    const skillsScore = this.calculateSkillsConfidence(data.skills);

    const overall = (personalInfoScore + experiencesScore + educationsScore + skillsScore) / 4;

    return {
      overall: Math.round(overall * 100) / 100,
      personalInfo: Math.round(personalInfoScore * 100) / 100,
      experiences: Math.round(experiencesScore * 100) / 100,
      educations: Math.round(educationsScore * 100) / 100,
      skills: Math.round(skillsScore * 100) / 100,
    };
  }

  private calculatePersonalInfoConfidence(personalInfo: ParsedResumeData["personalInfo"]): number {
    let score = 0;
    let maxScore = 0;

    // Name (most important)
    maxScore += 3;
    if (personalInfo.firstName && personalInfo.lastName) score += 3;
    else if (personalInfo.fullName) score += 2;

    // Email (very important)
    maxScore += 2;
    if (personalInfo.email) score += 2;

    // Phone (important)
    maxScore += 1.5;
    if (personalInfo.phone) score += 1.5;

    // Location (helpful)
    maxScore += 1;
    if (personalInfo.city || personalInfo.address) score += 1;

    // Professional links (helpful)
    maxScore += 1;
    if (personalInfo.linkedin || personalInfo.github || personalInfo.portfolio) score += 1;

    // Job title (helpful)
    maxScore += 0.5;
    if (personalInfo.jobTitle) score += 0.5;

    return maxScore > 0 ? score / maxScore : 0;
  }

  private calculateExperiencesConfidence(experiences: ParsedResumeData["experiences"]): number {
    if (experiences.length === 0) return 0;

    let totalScore = 0;
    for (const exp of experiences) {
      let expScore = 0;
      let maxExpScore = 0;

      // Title and company (essential)
      maxExpScore += 4;
      if (exp.title) expScore += 2;
      if (exp.company) expScore += 2;

      // Dates (important)
      maxExpScore += 2;
      if (exp.startDate) expScore += 1;
      if (exp.endDate || exp.isCurrent) expScore += 1;

      // Description (important)
      maxExpScore += 2;
      if (exp.description && exp.description.length > 20) expScore += 2;
      else if (exp.description) expScore += 1;

      // Location (helpful)
      maxExpScore += 1;
      if (exp.location || exp.city) expScore += 1;

      totalScore += maxExpScore > 0 ? expScore / maxExpScore : 0;
    }

    return totalScore / experiences.length;
  }

  private calculateEducationsConfidence(educations: ParsedResumeData["educations"]): number {
    if (educations.length === 0) return 0;

    let totalScore = 0;
    for (const edu of educations) {
      let eduScore = 0;
      let maxEduScore = 0;

      // Degree and institution (essential)
      maxEduScore += 4;
      if (edu.degree) eduScore += 2;
      if (edu.institution) eduScore += 2;

      // Field of study (important)
      maxEduScore += 1.5;
      if (edu.fieldOfStudy) eduScore += 1.5;

      // Dates (helpful)
      maxEduScore += 1;
      if (edu.startDate || edu.endDate) eduScore += 1;

      // Location (helpful)
      maxEduScore += 0.5;
      if (edu.location || edu.city) eduScore += 0.5;

      totalScore += maxEduScore > 0 ? eduScore / maxEduScore : 0;
    }

    return totalScore / educations.length;
  }

  private calculateSkillsConfidence(skills: ParsedResumeData["skills"]): number {
    if (skills.length === 0) return 0;
    if (skills.length < 3) return 0.3;
    if (skills.length < 8) return 0.7;
    return 1.0;
  }

  async close(): Promise<void> {
    // No cleanup needed for pdf-extraction
  }
}

export function convertParsedToResumeSection(parsed: ParsedResumeData): Partial<ResumeSection> {
  // Use the enhanced name parsing
  const firstName = parsed.personalInfo.firstName || "";
  const lastName = parsed.personalInfo.lastName || "";

  // Fallback to splitting fullName if firstName/lastName not available
  if (!firstName && !lastName && parsed.personalInfo.fullName) {
    const nameParts = parsed.personalInfo.fullName.split(" ");
    return {
      firstName: nameParts[0] || "",
      lastName: nameParts.slice(1).join(" ") || "",
      jobTitle: parsed.personalInfo.jobTitle || "",
      email: parsed.personalInfo.email || "",
      bio: parsed.summary || "",
      city: parsed.personalInfo.city || "",
      experiences: parsed.experiences.map(exp => ({
        jobTitle: exp.title,
        company: exp.company,
        description: exp.description,
        startDate: exp.startDate || "",
        endDate: exp.endDate || "",
        location: exp.location || exp.city || "",
      })),
      educations: parsed.educations.map(edu => ({
        institution: edu.institution,
        degree: edu.degree,
        fieldOfStudy: edu.fieldOfStudy || "",
        startDate: edu.startDate || "",
        endDate: edu.endDate || "",
        description: "",
      })),
      skills: parsed.skills.map(skill => ({
        name: typeof skill === 'string' ? skill : skill.name,
        proficiency: 3,
      })),
    };
  }

  return {
    firstName,
    lastName,
    jobTitle: parsed.personalInfo.jobTitle || "",
    email: parsed.personalInfo.email || "",
    bio: parsed.summary || "",
    city: parsed.personalInfo.city || "",
    experiences: parsed.experiences.map(exp => ({
      jobTitle: exp.title,
      company: exp.company,
      description: exp.description,
      startDate: exp.startDate || "",
      endDate: exp.endDate || "",
      location: exp.location || exp.city || "",
    })),
    educations: parsed.educations.map(edu => ({
      institution: edu.institution,
      degree: edu.degree,
      fieldOfStudy: edu.fieldOfStudy || "",
      startDate: edu.startDate || "",
      endDate: edu.endDate || "",
      description: "",
    })),
    skills: parsed.skills.map(skill => ({
      name: typeof skill === 'string' ? skill : skill.name,
      proficiency: 3,
    })),
  };
}
