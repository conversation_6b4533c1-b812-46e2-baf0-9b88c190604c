import type { ResumeSection } from "@/types/resume";

export interface ParsedResumeData {
  personalInfo: {
    name?: string;
    email?: string;
    phone?: string;
    location?: string;
    linkedin?: string;
    website?: string;
  };
  summary?: string;
  experiences: Array<{
    title: string;
    company: string;
    location?: string;
    startDate?: string;
    endDate?: string;
    description: string;
  }>;
  educations: Array<{
    degree: string;
    institution: string;
    location?: string;
    startDate?: string;
    endDate?: string;
    gpa?: string;
  }>;
  skills: string[];
  projects: Array<{
    name: string;
    description: string;
    technologies?: string[];
    startDate?: string;
    endDate?: string;
  }>;
  languages: Array<{
    name: string;
    proficiency?: string;
  }>;
  certifications: Array<{
    name: string;
    issuer?: string;
    issueDate?: string;
    expiryDate?: string;
    description?: string;
  }>;
}

class SectionParser {
  private readonly sectionKeywords = {
    experience: ["experience", "work experience", "professional experience", "employment", "employment history", "career history"],
    education: ["education", "academic background", "qualifications", "academic qualifications"],
    skills: ["skills", "technical skills", "core competencies", "expertise", "competencies"],
    projects: ["projects", "personal projects", "key projects", "project experience"],
    summary: ["summary", "objective", "profile", "about", "professional summary"],
    certifications: ["certifications", "certificates", "licenses", "professional certifications"],
    languages: ["languages", "language skills", "spoken languages"],
  };

  identifySections(lines: string[]): Record<string, string[]> {
    const sections: Record<string, string[]> = {};
    let currentSection = "personalInfo";
    sections[currentSection] = [];

    for (const line of lines) {
      const lowerLine = line.toLowerCase();
      const sectionName = this.getSectionName(lowerLine);

      if (sectionName) {
        currentSection = sectionName;
        if (!sections[currentSection]) {
          sections[currentSection] = [];
        }
      } else if (sections[currentSection]) {
        sections[currentSection].push(line);
      }
    }

    return sections;
  }

  private getSectionName(line: string): string | null {
    for (const [section, keywords] of Object.entries(this.sectionKeywords)) {
      if (keywords.some(keyword => line.includes(keyword) && line.length < 50)) {
        return section;
      }
    }
    return null;
  }
}

class PersonalInfoParser {
  parse(lines: string[]): ParsedResumeData["personalInfo"] {
    const personalInfo: ParsedResumeData["personalInfo"] = {};
    const topLines = lines.slice(0, 10);

    for (const line of topLines) {
      this.extractEmail(line, personalInfo);
      this.extractPhone(line, personalInfo);
      this.extractLinkedIn(line, personalInfo);
      this.extractWebsite(line, personalInfo);
      this.extractName(line, personalInfo);
    }

    return personalInfo;
  }

  private extractEmail(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    const emailMatch = line.match(/[\w.-]+@[\w.-]+\.\w+/);
    if (emailMatch && !personalInfo.email) {
      personalInfo.email = emailMatch[0];
    }
  }

  private extractPhone(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    const phoneMatch = line.match(/(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/);
    if (phoneMatch && !personalInfo.phone) {
      personalInfo.phone = phoneMatch[0];
    }
  }

  private extractLinkedIn(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    const linkedinMatch = line.match(/linkedin\.com\/in\/[\w-]+/);
    if (linkedinMatch && !personalInfo.linkedin) {
      personalInfo.linkedin = linkedinMatch[0];
    }
  }

  private extractWebsite(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    const websiteMatch = line.match(/(?:https?:\/\/)?(?:www\.)?[\w.-]+\.\w+(?:\/[\w.-]*)*(?:\?[\w=&.-]*)?/);
    if (websiteMatch && !websiteMatch[0].includes("linkedin") && !websiteMatch[0].includes("@") && !personalInfo.website) {
      personalInfo.website = websiteMatch[0];
    }
  }

  private extractName(line: string, personalInfo: ParsedResumeData["personalInfo"]) {
    if (!personalInfo.name && line.split(" ").length >= 2 && line.split(" ").length <= 5 && !personalInfo.email && !personalInfo.phone && !personalInfo.linkedin && !personalInfo.website && !/^(address|location|city|phone|email|summary|objective|experience|education|skills)/i.test(line) && line.length < 50) {
      personalInfo.name = line;
    }
  }
}

class ExperienceParser {
  parse(lines: string[]): ParsedResumeData["experiences"] {
    const experiences: ParsedResumeData["experiences"] = [];
    let currentExperience: Partial<ParsedResumeData["experiences"][0]> | null = null;
    let descriptionLines: string[] = [];

    const commitExperience = () => {
      if (currentExperience) {
        currentExperience.description = descriptionLines.join(' ').trim();
        // Basic validation to ensure we have at least a title or company
        if (currentExperience.title || currentExperience.company) {
          experiences.push(currentExperience as ParsedResumeData["experiences"][0]);
        }
        currentExperience = null;
        descriptionLines = [];
      }
    };

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (this.isDateRangeLine(line)) {
            commitExperience();
            currentExperience = this.parseDateRange(line);

            // The next lines are often location, title, company.
            // We look ahead a few lines to find them.
            let consumed = 0;
            const lookahead = lines.slice(i + 1, i + 4);

            // Check for location (e.g., "Remote", "Freelance")
            if (lookahead[0] && (lookahead[0].toLowerCase().includes('remote') || lookahead[0].toLowerCase().includes('freelance'))) {
                currentExperience.location = lookahead[0];
                consumed++;
            }

            // Check for job title
            if (lookahead[consumed] && this.looksLikeJobTitle(lookahead[consumed])) {
                currentExperience.title = lookahead[consumed];
                consumed++;
            }
            
            // Assume the next line is the company
            if (lookahead[consumed] && !this.isDateRangeLine(lookahead[consumed])) {
                 currentExperience.company = lookahead[consumed];
                 consumed++;
            }
            
            i += consumed; // Advance the loop index by the number of lines we consumed

        } else if (currentExperience) {
            // If we are in the middle of an experience entry, this line is part of the description.
            descriptionLines.push(line);
        }
    }

    commitExperience(); // Commit the very last experience entry
    return experiences;
  }

  private isDateRangeLine(line: string): boolean {
    // Checks for a date pattern like MM/YYYY, Month YYYY, or YYYY, followed by a dash.
    return /(\d{1,2}\/\d{4}|\b(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b\s+\d{4}|\d{4})\s*[-–]/.test(line);
  }

  private looksLikeJobTitle(line: string): boolean {
    const jobTitlePatterns = [/\b(engineer|developer|manager|analyst|specialist|coordinator|director|lead)\b/i, /\b(senior|junior|principal|staff|associate)\b/i];
    // Ensure it's not a description line by checking length and common description starters
    return jobTitlePatterns.some(pattern => pattern.test(line)) && line.length < 100 && !line.startsWith('•') && !line.startsWith('-');
  }

  private parseDateRange(line: string): { startDate?: string; endDate?: string } {
    const rangeMatch = line.match(/(\d{1,2}\/\d{4}|\w+\s+\d{4}|\d{4})\s*[-–]\s*(\d{1,2}\/\d{4}|\w+\s+\d{4}|\d{4}|present)/i);
    if (rangeMatch) {
      const start = rangeMatch[1];
      const end = rangeMatch[2];
      return { 
        startDate: this.normalizeDate(start), 
        endDate: end.toLowerCase() === 'present' ? 'Present' : this.normalizeDate(end) 
      };
    }
    return {};
  }
  
  private normalizeDate(dateStr: string): string {
    // Handle MM/YYYY format specifically
    if (/^\d{1,2}\/\d{4}$/.test(dateStr)) {
        const [month, year] = dateStr.split('/');
        return `${year}-${month.padStart(2, '0')}`;
    }
    // Handle other common date formats
    const date = new Date(dateStr);
    return !isNaN(date.getTime()) ? date.toISOString().split("T")[0] : dateStr;
  }
}

class EducationParser {
  parse(lines: string[]): ParsedResumeData["educations"] {
    const educations: ParsedResumeData["educations"] = [];
    let currentEducation: Partial<ParsedResumeData["educations"][0]> = {};

    for (const line of lines) {
      if (this.looksLikeDegree(line)) {
        if (currentEducation.degree || currentEducation.institution) {
          educations.push(currentEducation as ParsedResumeData["educations"][0]);
        }
        currentEducation = this.parseDegreeLine(line);
      } else if (this.looksLikeDateRange(line)) {
        const dates = this.parseDateRange(line);
        currentEducation.startDate = dates.start;
        currentEducation.endDate = dates.end;
      } else if (line.toLowerCase().includes("gpa")) {
        const gpaMatch = line.match(/(\d+\.\d+)/);
        if (gpaMatch) {
          currentEducation.gpa = gpaMatch[1];
        }
      }
    }

    if (currentEducation.degree || currentEducation.institution) {
      educations.push(currentEducation as ParsedResumeData["educations"][0]);
    }

    return educations;
  }

  private looksLikeDegree(line: string): boolean {
    const degreePatterns = [/\b(bachelor|master|phd|doctorate|associate|diploma|certificate)\b/i, /\b(bs|ba|ms|ma|mba|phd|md|jd)\b/i, /\bin\s+/i];
    return degreePatterns.some(pattern => pattern.test(line));
  }

  private looksLikeDateRange(line: string): boolean {
    return /\d{4}|\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b|\bpresent\b/i.test(line);
  }

  private parseDegreeLine(line: string): Partial<ParsedResumeData["educations"][0]> {
    const inMatch = line.match(/^(.*?)\s+in\s+(.*?)(?:\s+from\s+(.+))?$/i);
    if (inMatch) {
      return { degree: inMatch[1].trim(), institution: inMatch[3]?.trim() || "" };
    }
    return { degree: line };
  }

  private parseDateRange(line: string): { start?: string; end?: string } {
    const rangeMatch = line.match(/(\w+\s+\d{4}|\d{4})\s*[-–]\s*(\w+\s+\d{4}|\d{4}|present)/i);
    if (rangeMatch) {
      return { start: this.normalizeDate(rangeMatch[1]), end: rangeMatch[2].toLowerCase() === "present" ? "" : this.normalizeDate(rangeMatch[2]) };
    }
    return {};
  }

  private normalizeDate(dateStr: string): string {
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? dateStr : date.toISOString().split("T")[0];
  }
}

class SkillsParser {
  parse(lines: string[]): string[] {
    const skills: string[] = [];
    for (const line of lines) {
      // Replace '#' with a comma, then split by common delimiters.
      // This handles "#skill1#skill2" and "skill1, skill2".
      const skillItems = line.replace(/#/g, ',').split(/[,•·|]/).map(s => s.trim()).filter(s => s.length > 0);
      skills.push(...skillItems);
    }
    // Filter out non-skill-like items.
    return skills.filter(skill => skill.length > 1 && skill.length < 35 && !skill.match(/^\d+$/) && !skill.toLowerCase().includes("years"));
  }
}

class ProjectsParser {
  parse(lines: string[]): ParsedResumeData["projects"] {
    const projects: ParsedResumeData["projects"] = [];
    let currentProject: Partial<ParsedResumeData["projects"][0]> = {};
    let descriptionLines: string[] = [];

    for (const line of lines) {
      if (this.looksLikeProjectTitle(line)) {
        if (currentProject.name) {
          currentProject.description = descriptionLines.join(" ");
          projects.push(currentProject as ParsedResumeData["projects"][0]);
        }
        currentProject = { name: line };
        descriptionLines = [];
      } else if (line.length > 10) {
        descriptionLines.push(line);
      }
    }

    if (currentProject.name) {
      currentProject.description = descriptionLines.join(" ");
      projects.push(currentProject as ParsedResumeData["projects"][0]);
    }

    return projects;
  }

  private looksLikeProjectTitle(line: string): boolean {
    return line.length < 80 && !line.includes("•") && !line.includes("-");
  }
}

class SummaryParser {
  parse(lines: string[]): string {
    return lines.join(" ");
  }
}

class CertificationsParser {
  parse(lines: string[]): ParsedResumeData["certifications"] {
    const certifications: ParsedResumeData["certifications"] = [];
    let currentCert: Partial<ParsedResumeData["certifications"][0]> = {};
    let descriptionLines: string[] = [];

    for (const line of lines) {
      if (this.looksLikeCertificationTitle(line)) {
        if (currentCert.name) {
          currentCert.description = descriptionLines.join(" ");
          certifications.push(currentCert as ParsedResumeData["certifications"][0]);
        }
        currentCert = this.parseCertificationLine(line);
        descriptionLines = [];
      } else if (this.looksLikeDateRange(line)) {
        const dates = this.parseDateRange(line);
        currentCert.issueDate = dates.start;
        currentCert.expiryDate = dates.end;
      } else if (line.toLowerCase().includes("issued by") || line.toLowerCase().includes("issuer")) {
        const issuerMatch = line.match(/(?:issued by|issuer):?\s*(.+)/i);
        if (issuerMatch) {
          currentCert.issuer = issuerMatch[1].trim();
        }
      } else if (line.length > 10) {
        descriptionLines.push(line);
      }
    }

    if (currentCert.name) {
      currentCert.description = descriptionLines.join(" ");
      certifications.push(currentCert as ParsedResumeData["certifications"][0]);
    }

    return certifications;
  }

  private looksLikeCertificationTitle(line: string): boolean {
    return this.looksLikeCertification(line) && line.length < 100 && !line.includes("•") && !line.includes("-");
  }

  private looksLikeCertification(line: string): boolean {
    const certificationPatterns = [/\b(certified|certification|certificate|license|licensed)\b/i, /\b(aws|azure|google cloud|gcp|microsoft|oracle|cisco|comptia)\b/i, /\b(pmp|cissp|ceh|ccna|mcse|rhce|cka|ckad)\b/i, /\b(issued by|expires|valid until)\b/i];
    return certificationPatterns.some(pattern => pattern.test(line));
  }

  private parseCertificationLine(line: string): Partial<ParsedResumeData["certifications"][0]> {
    const fromMatch = line.match(/^(.*?)\s+(?:from|by)\s+(.+)$/i);
    if (fromMatch) {
      return { name: fromMatch[1].trim(), issuer: fromMatch[2].trim() };
    }
    return { name: line.trim() };
  }

  private looksLikeDateRange(line: string): boolean {
    return /\d{4}|\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b|\bpresent\b/i.test(line);
  }

  private parseDateRange(line: string): { start?: string; end?: string } {
    const rangeMatch = line.match(/(\w+\s+\d{4}|\d{4})\s*[-–]\s*(\w+\s+\d{4}|\d{4}|present)/i);
    if (rangeMatch) {
      return { start: this.normalizeDate(rangeMatch[1]), end: rangeMatch[2].toLowerCase() === "present" ? "" : this.normalizeDate(rangeMatch[2]) };
    }
    return {};
  }

  private normalizeDate(dateStr: string): string {
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? dateStr : date.toISOString().split("T")[0];
  }
}

class LanguagesParser {
  parse(lines: string[]): ParsedResumeData["languages"] {
    const languages: ParsedResumeData["languages"] = [];
    const proficiencyLevels = ['native', 'fluent', 'proficient', 'conversational', 'basic'];

    for (const line of lines) {
      const items = line.split(/[,|]/).map(item => item.trim());

      for (const item of items) {
        let languageName = item;
        let proficiency: string | undefined;

        const proficiencyMatch = item.match(/\((.*?)\)|:\s*(.*)/);
        if (proficiencyMatch) {
          const potentialProficiency = (proficiencyMatch[1] || proficiencyMatch[2] || '').toLowerCase().trim();
          if (proficiencyLevels.includes(potentialProficiency)) {
            proficiency = potentialProficiency;
            languageName = item.replace(proficiencyMatch[0], '').trim();
          }
        }
        
        if (languageName) {
            languages.push({ name: languageName, proficiency });
        }
      }
    }
    return languages;
  }
}

export class PDFResumeParser {
  private sectionParser = new SectionParser();
  private personalInfoParser = new PersonalInfoParser();
  private experienceParser = new ExperienceParser();
  private educationParser = new EducationParser();
  private skillsParser = new SkillsParser();
  private projectsParser = new ProjectsParser();
  private summaryParser = new SummaryParser();
  private certificationsParser = new CertificationsParser();
  private languagesParser = new LanguagesParser();

  async parsePDFResume(pdfBuffer: Buffer): Promise<ParsedResumeData> {
    try {
      const pdfExtraction = (await import("pdf-extraction")).default;
      const extractedData = await pdfExtraction(pdfBuffer);
      const extractedText = extractedData.text;

      console.log("Extracted Text:", extractedText);

      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error("No text content found in PDF. The PDF might be image-based or corrupted.");
      }

      return this.parseResumeText(extractedText);
    } catch (error) {
      console.error("Error parsing PDF resume:", error);
      if (error instanceof Error) {
        throw new Error(`Failed to parse PDF resume: ${error.message}`);
      }
      throw new Error("Failed to parse PDF resume. Please ensure the file is a valid PDF.");
    }
  }

  private parseResumeText(text: string): ParsedResumeData {
    const lines = text.split("\n").map(line => line.trim()).filter(line => line.length > 0);
    const sections = this.sectionParser.identifySections(lines);

    return {
      personalInfo: this.personalInfoParser.parse(sections.personalInfo || []),
      experiences: this.experienceParser.parse(sections.experience || []),
      educations: this.educationParser.parse(sections.education || []),
      skills: this.skillsParser.parse(sections.skills || []),
      projects: this.projectsParser.parse(sections.projects || []),
      summary: this.summaryParser.parse(sections.summary || []),
      certifications: this.certificationsParser.parse(sections.certifications || []),
      languages: this.languagesParser.parse(sections.languages || []),
    };
  }

  async close(): Promise<void> {
    // No cleanup needed for pdf-extraction
  }
}

export function convertParsedToResumeSection(parsed: ParsedResumeData): Partial<ResumeSection> {
  const nameParts = parsed.personalInfo.name?.split(" ") || [];
  const firstName = nameParts[0] || "";
  const lastName = nameParts.slice(1).join(" ") || "";

  return {
    firstName,
    lastName,
    email: parsed.personalInfo.email || "",
    bio: parsed.summary || "",
    city: parsed.personalInfo.location || "",
    experiences: parsed.experiences.map(exp => ({
      jobTitle: exp.title,
      company: exp.company,
      description: exp.description,
      startDate: exp.startDate || "",
      endDate: exp.endDate || "",
      location: exp.location || "",
    })),
    educations: parsed.educations.map(edu => ({
      institution: edu.institution,
      degree: edu.degree,
      fieldOfStudy: "",
      startDate: edu.startDate || "",
      endDate: edu.endDate || "",
      description: "",
    })),
    skills: parsed.skills.map(skill => ({
      name: skill,
      proficiency: 3,
    })),
  };
}
