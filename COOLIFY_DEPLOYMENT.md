# Coolify Browserless Deployment Guide

This guide shows you how to deploy Browserless from Coolify's marketplace to handle PDF generation for your QuickCV app.

## Architecture Overview

```
Vercel (QuickCV App) → WebSocket → Coolify (Browserless Service)
```

- **Vercel**: Hosts your Next.js QuickCV application
- **Coolify**: Hosts Browserless service from marketplace
- **Connection**: WebSocket connection with token authentication

## Prerequisites

- Cloud Coolify account or self-hosted Coolify
- No custom domain configuration needed
- Automatic URL generation with sslip.io

## Step 1: Deploy Browserless from Marketplace

1. **Go to Coolify Dashboard**
2. **Click "New Resource"**
3. **Select "Service" from marketplace**
4. **Find and select "Browserless"**
5. **Configure basic settings**:
   - Name: `browserless` (or your preferred name)
   - Environment: `production`
6. **Deploy the service**

Coolify will automatically:
- Generate a secure authentication token
- Create a public URL with sslip.io
- Set up environment variables
- Configure networking and SSL

## Step 2: Get Service Configuration

After deployment, Coolify provides:

1. **Service URL** (in environment variables):
   ```
   SERVICE_FQDN_BROWSERLESS=http://browserless-xxxxx.**************.sslip.io
   ```

2. **Authentication Token** (auto-generated):
   ```
   SERVICE_PASSWORD_BROWSERLESS=your_secure_token
   TOKEN=$SERVICE_PASSWORD_BROWSERLESS
   ```

3. **Test the service**:
   ```bash
   curl "http://browserless-xxxxx.**************.sslip.io/json/version?token=your_secure_token"
   ```

## Step 3: Configure Vercel Environment

1. **Add environment variable to Vercel**:
   ```bash
   BROWSER_WS_ENDPOINT=ws://browserless-xxxxx.**************.sslip.io?token=your_secure_token
   ```

2. **Deploy to Vercel**:
   - Push changes to your repository
   - Vercel will automatically redeploy

## Step 4: Test PDF Generation

1. **Go to your Vercel app**
2. **Create/edit a resume** 
3. **Click "Download PDF"**
4. **PDF should generate successfully**

Expected health check response:
```json
{
  "Browser": "Chrome/139.0.7258.5",
  "Protocol-Version": "1.3",
  "User-Agent": "Mozilla/5.0...",
  "V8-Version": "**********",
  "WebKit-Version": "537.36"
}
```

## Monitoring and Troubleshooting

### Coolify Monitoring
- **Service Logs**: Check Chrome service logs in Coolify
- **Resource Usage**: Monitor CPU/Memory usage
- **Health Checks**: Built-in health monitoring

### Common Issues

#### Connection Refused
```
Error: connect ECONNREFUSED
```
**Solution**: Check if Chrome service is running and domain is accessible

#### WebSocket Connection Failed
```
Error: WebSocket connection failed
```
**Solutions**:
1. Verify SSL certificate is working
2. Check if WebSocket upgrades are enabled
3. Ensure CORS is configured correctly

#### Timeout Errors
```
Error: Navigation timeout exceeded
```
**Solutions**:
1. Increase `CONNECTION_TIMEOUT` in Chrome service
2. Check if your resume page loads correctly
3. Monitor server resources

### Performance Optimization

#### Resource Limits
```yaml
deploy:
  resources:
    limits:
      memory: 1G
      cpus: '1.0'
```

#### Scaling
- For high traffic, increase `MAX_CONCURRENT_SESSIONS`
- Consider running multiple Chrome service instances
- Use Coolify's load balancing for multiple instances

## Security Considerations

### Authentication
- Always use a secure token for `CHROME_TOKEN`
- Rotate tokens periodically
- Never expose tokens in client-side code

### Network Security
- Chrome service only accessible via HTTPS/WSS
- Rate limiting configured in nginx
- Debug endpoints blocked from external access

### Firewall Rules
```bash
# Allow only necessary ports
ufw allow 80/tcp   # HTTP (redirects to HTTPS)
ufw allow 443/tcp  # HTTPS/WSS
```

## Cost Optimization

### Resource Management
- **Memory**: Chrome service uses ~500MB-1GB RAM
- **CPU**: Minimal when idle, bursts during PDF generation
- **Storage**: Minimal storage requirements

### Scaling Strategy
- Start with 1 instance
- Monitor usage patterns
- Scale horizontally if needed

## Backup and Maintenance

### Service Updates
1. **Update Chrome image**:
   ```bash
   docker pull browserless/chrome:latest
   ```
2. **Redeploy in Coolify**
3. **Test PDF generation**

### Monitoring Alerts
Set up alerts for:
- Service downtime
- High memory usage (>90%)
- High CPU usage (>90%)
- Connection errors

## Support

### Logs Location
- **Coolify**: Service logs in dashboard
- **Chrome**: Browser logs accessible via service logs

### Debug Mode
For debugging, temporarily enable:
```
ENABLE_DEBUGGER=true
```
Then access debug interface at: `https://chrome.yourdomain.com/`

**⚠️ Important**: Disable debug mode in production!

---

## Quick Checklist

- [ ] Chrome service deployed to Coolify
- [ ] Domain configured with SSL
- [ ] Environment variables set
- [ ] Vercel environment updated
- [ ] Health check passing
- [ ] PDF generation tested
- [ ] Monitoring configured
- [ ] Security tokens set
- [ ] Debug mode disabled

Your Chrome service is now ready to handle PDF generation for your Vercel-hosted QuickCV application!