import createNextIntlPlugin from "next-intl/plugin";
import { fileURLToPath } from "url";

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Performance optimizations
  compress: true,
  devIndicators: false,
  poweredByHeader: false,
  // Allow cross-origin requests from Docker containers in development
  allowedDevOrigins: process.env.NODE_ENV === "development" ? ["host.docker.internal"] : [],
  images: {
    formats: ["image/avif", "image/webp"],
    minimumCacheTTL: 60,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "ui-avatars.com",
      },
      {
        protocol: "https",
        hostname: "utfs.io",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
      {
        protocol: "https",
        hostname: "randomuser.me",
      },
    ],
  },
  experimental: {
    serverActions: {
      bodySizeLimit: "4mb",
    },
    reactCompiler:true
  },
  
  // SWC compiler optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
  // Improve build stability and caching
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // Prevent build manifest race conditions in development
      config.watchOptions = {
        ...config.watchOptions,
        ignored: ["**/node_modules", "**/.next"],
      };
    }

    // Enable build caching
    if (!dev) {
      config.cache = {
        type: "filesystem",
        buildDependencies: {
          config: [fileURLToPath(import.meta.url)],
        },
      };
    }

    return config;
  },
};

export default withNextIntl(nextConfig);
