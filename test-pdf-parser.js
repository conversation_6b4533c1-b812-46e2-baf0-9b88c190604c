// Simple test script to verify the enhanced PDF parser
const { PDFResumeParser } = require('./lib/pdf-resume-parser.ts');
const fs = require('fs');

async function testParser() {
  console.log('🧪 Testing Enhanced PDF Resume Parser...\n');
  
  // Test with sample text data (simulating PDF extraction)
  const sampleResumeText = `
<PERSON>
Senior Software Engineer
<EMAIL> | (555) 123-4567
San Francisco, CA
https://github.com/johnsmith | https://linkedin.com/in/johnsmith

PROFESSIONAL SUMMARY
Experienced software engineer with 8+ years developing scalable web applications and leading cross-functional teams.

EXPERIENCE
2020 - Present
Senior Software Engineer
Tech Corp Inc, San Francisco, CA
• Led development of microservices architecture serving 1M+ users
• Mentored junior developers and improved team productivity by 30%
• Implemented CI/CD pipelines reducing deployment time by 50%

2018 - 2020
Software Engineer
StartupXYZ, Remote
• Built full-stack web applications using React and Node.js
• Collaborated with product team to define technical requirements
• Optimized database queries improving performance by 40%

EDUCATION
2014 - 2018
Bachelor of Science in Computer Science
Stanford University, Stanford, CA
GPA: 3.8

SKILLS
JavaScript, Python, React, Node.js, AWS, Docker, Kubernetes, PostgreSQL, MongoDB, Git, Agile

PROJECTS
E-commerce Platform
Built a full-stack e-commerce platform with React, Node.js, and PostgreSQL
Technologies: React, Node.js, PostgreSQL, Stripe API

CERTIFICATIONS
AWS Certified Solutions Architect
Amazon Web Services, 2021

LANGUAGES
English (Native)
Spanish (Conversational)

AWARDS
Employee of the Year 2021
Tech Corp Inc

VOLUNTEER EXPERIENCE
2019 - Present
Volunteer Coding Instructor
Code for Good, San Francisco, CA
Teaching programming fundamentals to underserved communities

REFERENCES
Jane Doe
Engineering Manager, Tech Corp Inc
<EMAIL> | (555) 987-6543

HOBBIES
Photography, Rock Climbing, Chess, Reading
  `;

  try {
    const parser = new PDFResumeParser();
    const result = parser.parseResumeText(sampleResumeText);
    
    console.log('✅ Parsing completed successfully!\n');
    
    // Test personal info extraction
    console.log('👤 Personal Information:');
    console.log(`   Name: ${result.personalInfo.firstName} ${result.personalInfo.lastName}`);
    console.log(`   Job Title: ${result.personalInfo.jobTitle || 'Not found'}`);
    console.log(`   Email: ${result.personalInfo.email || 'Not found'}`);
    console.log(`   Phone: ${result.personalInfo.phone || 'Not found'}`);
    console.log(`   City: ${result.personalInfo.city || 'Not found'}`);
    console.log(`   GitHub: ${result.personalInfo.github || 'Not found'}`);
    console.log(`   LinkedIn: ${result.personalInfo.linkedin || 'Not found'}`);
    
    // Test experiences
    console.log(`\n💼 Work Experience (${result.experiences.length} found):`);
    result.experiences.forEach((exp, i) => {
      console.log(`   ${i + 1}. ${exp.title} at ${exp.company}`);
      console.log(`      Duration: ${exp.startDate} - ${exp.endDate}`);
      console.log(`      Location: ${exp.location || exp.city || 'Not specified'}`);
    });
    
    // Test education
    console.log(`\n🎓 Education (${result.educations.length} found):`);
    result.educations.forEach((edu, i) => {
      console.log(`   ${i + 1}. ${edu.degree} in ${edu.fieldOfStudy || 'Not specified'}`);
      console.log(`      Institution: ${edu.institution}`);
      console.log(`      Duration: ${edu.startDate} - ${edu.endDate}`);
    });
    
    // Test skills
    console.log(`\n🛠️ Skills (${result.skills.length} found):`);
    const skillsByCategory = {};
    result.skills.forEach(skill => {
      const category = skill.category || 'Other';
      if (!skillsByCategory[category]) skillsByCategory[category] = [];
      skillsByCategory[category].push(skill.name);
    });
    
    Object.entries(skillsByCategory).forEach(([category, skills]) => {
      console.log(`   ${category}: ${skills.join(', ')}`);
    });
    
    // Test other sections
    console.log(`\n📜 Certifications: ${result.certifications.length} found`);
    console.log(`🏆 Awards: ${result.awards.length} found`);
    console.log(`🤝 Volunteering: ${result.volunteering.length} found`);
    console.log(`👥 References: ${result.references.length} found`);
    console.log(`🎯 Hobbies: ${result.hobbies.length} found`);
    console.log(`🔗 Profiles: ${result.profiles.length} found`);
    
    // Test confidence scores
    console.log(`\n📊 Confidence Scores:`);
    console.log(`   Overall: ${(result.confidence.overall * 100).toFixed(1)}%`);
    console.log(`   Personal Info: ${(result.confidence.personalInfo * 100).toFixed(1)}%`);
    console.log(`   Experiences: ${(result.confidence.experiences * 100).toFixed(1)}%`);
    console.log(`   Education: ${(result.confidence.educations * 100).toFixed(1)}%`);
    console.log(`   Skills: ${(result.confidence.skills * 100).toFixed(1)}%`);
    
    console.log('\n🎉 All tests passed! The enhanced parser is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
testParser();
