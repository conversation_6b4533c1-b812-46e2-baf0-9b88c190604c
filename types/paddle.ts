// Paddle API types and interfaces

export interface PaddleTransaction {
  id: string;
  status: string;
  created_at: string;
  details: {
    totals: {
      grand_total: string;
      currency_code: string;
    };
  };
  items: Array<{
    price: {
      name: string;
    };
  }>;
  invoice_number?: string;
  receipt_data?: {
    url?: string;
  };
}

export interface PaddleApiResponse<T> {
  data: T[];
  meta: {
    request_id: string;
    pagination?: {
      per_page: number;
      next?: string;
      has_more: boolean;
      estimated_total: number;
    };
  };
}

export interface Invoice {
  id: string;
  date: string;
  amount: string;
  currency: string;
  status: string;
  description: string;
  downloadUrl?: string;
  invoiceNumber?: string;
}
