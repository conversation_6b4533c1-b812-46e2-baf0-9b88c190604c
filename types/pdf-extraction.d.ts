declare module 'pdf-extraction' {
  interface ExtractedData {
    text: string;
    pages: number;
    info?: {
      title?: string;
      author?: string;
      subject?: string;
      keywords?: string;
      creator?: string;
      producer?: string;
      creationDate?: Date;
      modDate?: Date;
    };
  }

  function pdfExtraction(buffer: Buffer): Promise<ExtractedData>;
  export default pdfExtraction;
}