"use client";

import { <PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { ReactNode } from "react";

interface EmptyStateProps {
  // Visual configuration
  icon: string;
  title: string;
  description: string;

  // Action button (optional)
  actionButton?: ReactNode;

  // Style variants
  variant?: "default" | "gradient" | "minimal";
  size?: "sm" | "md" | "lg";

  // Layout options
  className?: string;
  centered?: boolean;

  // Icon customization
  iconSize?: "sm" | "md" | "lg";
  iconColor?: string;
  showIconBadge?: boolean;
  iconBadge?: string;
}

const VARIANT_STYLES = {
  default: {
    container: "py-20 px-4",
    iconContainer: "w-24 h-24 bg-primary/10 rounded-full",
    icon: "w-12 h-12 text-primary/60",
    title: "text-2xl font-bold text-foreground",
    description: "text-foreground/60",
  },
  gradient: {
    container: "py-16",
    iconContainer: "w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full",
    icon: "w-12 h-12 text-white",
    title: "text-2xl font-bold text-gray-900 dark:text-gray-100",
    description: "text-gray-600 dark:text-gray-400",
  },
  minimal: {
    container: "py-12 px-4",
    iconContainer: "w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg",
    icon: "w-8 h-8 text-gray-600 dark:text-gray-400",
    title: "text-xl font-semibold text-foreground",
    description: "text-sm text-foreground/60",
  },
};

const SIZE_STYLES = {
  sm: {
    container: "max-w-sm",
    titleClass: "text-lg",
    descriptionClass: "text-sm",
    spacing: "mb-4",
  },
  md: {
    container: "max-w-md",
    titleClass: "text-2xl",
    descriptionClass: "text-base",
    spacing: "mb-8",
  },
  lg: {
    container: "max-w-lg",
    titleClass: "text-3xl",
    descriptionClass: "text-lg",
    spacing: "mb-8",
  },
};

const ICON_SIZES = {
  sm: { container: "w-16 h-16", icon: "w-8 h-8", badge: "w-6 h-6" },
  md: { container: "w-20 h-20", icon: "w-10 h-10", badge: "w-7 h-7" },
  lg: { container: "w-24 h-24", icon: "w-12 h-12", badge: "w-8 h-8" },
};

export default function EmptyState({
  icon,
  title,
  description,
  actionButton,
  variant = "default",
  size = "md",
  className = "",
  centered = true,
  iconSize = "md",
  iconColor,
  showIconBadge = false,
  iconBadge = "lucide:plus",
}: EmptyStateProps) {
  const variantStyle = VARIANT_STYLES[variant];
  const sizeStyle = SIZE_STYLES[size];
  const iconSizes = ICON_SIZES[iconSize];

  const containerClasses = [
    "flex flex-col items-center justify-center",
    variantStyle.container,
    centered ? "text-center" : "",
    className,
  ]
    .filter(Boolean)
    .join(" ");

  const contentClasses = [sizeStyle.container, centered ? "mx-auto" : ""].filter(Boolean).join(" ");

  return (
    <div className={containerClasses}>
      <div className={contentClasses}>
        {/* Icon with optional badge */}
        <div className="relative mb-8">
          <div
            className={`${iconSizes.container} ${variantStyle.iconContainer} flex items-center justify-center mx-auto`}
          >
            <Icon icon={icon} className={`${iconSizes.icon} ${iconColor || variantStyle.icon}`} />
          </div>

          {showIconBadge && (
            <div
              className={`absolute -top-2 -right-2 ${iconSizes.badge} bg-warning/20 rounded-full flex items-center justify-center`}
            >
              <Icon icon={iconBadge} className="w-4 h-4 text-warning" />
            </div>
          )}
        </div>

        {/* Title */}
        <h3 className={`${variantStyle.title} ${sizeStyle.titleClass} mb-2`}>{title}</h3>

        {/* Description */}
        <p
          className={`${variantStyle.description} ${sizeStyle.descriptionClass} ${centered ? "text-center" : ""} ${sizeStyle.spacing}`}
        >
          {description}
        </p>

        {/* Action Button */}
        {actionButton && <div className="mt-4">{actionButton}</div>}
      </div>
    </div>
  );
}

// Convenience wrapper for common gradient empty state
export function GradientEmptyState(props: Omit<EmptyStateProps, "variant">) {
  return <EmptyState {...props} variant="gradient" />;
}

// Convenience wrapper for minimal empty state
export function MinimalEmptyState(props: Omit<EmptyStateProps, "variant">) {
  return <EmptyState {...props} variant="minimal" />;
}
