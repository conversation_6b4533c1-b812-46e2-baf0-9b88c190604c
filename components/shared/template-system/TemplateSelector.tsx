import React from "react";
import { TemplateSelectorProps } from "./types";

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  templates,
  selectedTemplate,
  onTemplateSelect,
  className = "",
}) => {
  return (
    <div className={`template-selector ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template) => (
          <button
            key={template.slug}
            type="button"
            className={`template-option p-4 border rounded-lg transition-all hover:shadow-md ${
              selectedTemplate === template.slug
                ? "border-blue-500 bg-blue-50"
                : "border-gray-200 hover:border-gray-300"
            }`}
            onClick={() => onTemplateSelect(template.slug)}
          >
            <h3 className="font-semibold text-gray-900 mb-2">{template.name}</h3>
            {template.category && (
              <span className="inline-block px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                {template.category}
              </span>
            )}
            {template.tags && (
              <div className="mt-2 flex flex-wrap gap-1">
                {template.tags.map((tag) => (
                  <span key={tag} className="inline-block px-1 py-0.5 bg-gray-200 text-gray-600 text-xs rounded">
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};
