import { TemplateRegistryEntry } from "./types";

export class TemplateRegistry<T = any> {
  private templates: Map<string, TemplateRegistryEntry<T>> = new Map();

  register(entry: TemplateRegistryEntry<T>): void {
    this.templates.set(entry.slug, entry);
  }

  get(slug: string): TemplateRegistryEntry<T> | null {
    return this.templates.get(slug) || null;
  }

  getAll(): TemplateRegistryEntry<T>[] {
    return Array.from(this.templates.values());
  }

  getByCategory(category: string): TemplateRegistryEntry<T>[] {
    return this.getAll().filter((template) => template.category === category);
  }

  exists(slug: string): boolean {
    return this.templates.has(slug);
  }

  getAvailableSlugs(): string[] {
    return Array.from(this.templates.keys());
  }
}
