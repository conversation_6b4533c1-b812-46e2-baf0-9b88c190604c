"use client";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";

const LoaderComponent = () => {
  const tCommon = useTranslations("common");
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <Icon icon="tabler:loader-2" className="w-8 h-8 animate-spin" />
      <span className="ml-2">{tCommon("loading")}</span>
    </div>
  );
};

export default LoaderComponent;
