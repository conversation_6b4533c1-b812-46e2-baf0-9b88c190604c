"use client";

import { But<PERSON>, Dropdown, DropdownI<PERSON>, DropdownMenu, DropdownTrigger } from "@heroui/react";
import { useLocale } from "next-intl";
import { Link } from "@/i18n/navigation";

export function LanguageSwitcher() {
  const locale = useLocale();

  const languages = [
    { code: "ar", name: "العربية", flag: "🇸🇦" },
    { code: "en", name: "English", flag: "🇺🇸" },
  ];

  const currentLanguage = languages.find((lang) => lang.code === locale) || languages[0];

  return (
    <Dropdown>
      <DropdownTrigger>
        <Button className="min-w-unit-12 h-unit-8 px-2" size="sm" variant="light">
          <span className="text-lg">{currentLanguage.flag}</span>
          <span className="hidden sm:inline ms-1">{currentLanguage.name}</span>
        </Button>
      </DropdownTrigger>
      <DropdownMenu aria-label="Language selection" selectedKeys={[locale]} selectionMode="single">
        {languages.map((language) => (
          <DropdownItem key={language.code} startContent={<span className="text-lg">{language.flag}</span>}>
            <Link href="/" locale={language.code} className="flex items-center gap-2">
              {language.name}
            </Link>
          </DropdownItem>
        ))}
      </DropdownMenu>
    </Dropdown>
  );
}
