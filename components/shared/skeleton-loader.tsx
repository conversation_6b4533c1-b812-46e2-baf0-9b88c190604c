"use client";

import { Card, CardBody, Skeleton } from "@heroui/react";

interface SkeletonLoaderProps {
  // Layout
  count?: number;
  gridCols?: 1 | 2 | 3 | 4 | 5;
  className?: string;

  // Card options
  variant?: "card" | "list" | "custom";
  cardClassName?: string;

  // Skeleton configuration
  showImage?: boolean;
  imageHeight?: string;
  showTitle?: boolean;
  titleWidth?: string;
  showSubtitle?: boolean;
  subtitleWidth?: string;
  showBody?: boolean;
  bodyLines?: number;

  // Animation
  animationDelay?: boolean;

  // Custom content
  children?: React.ReactNode;
}

const GRID_CLASSES = {
  1: "grid-cols-1",
  2: "grid-cols-1 sm:grid-cols-2",
  3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
  4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
  5: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5",
};

export default function SkeletonLoader({
  count = 6,
  gridCols = 3,
  className = "",
  variant = "card",
  cardClassName = "",
  showImage = true,
  imageHeight = "h-[320px]",
  showTitle = true,
  titleWidth = "w-3/4",
  showSubtitle = true,
  subtitleWidth = "w-1/2",
  showBody = false,
  bodyLines = 3,
  animationDelay = true,
  children,
}: SkeletonLoaderProps) {
  const renderCardSkeleton = (index: number) => (
    <div
      key={index}
      className={`w-full ${gridCols >= 4 ? "max-w-[280px]" : ""} ${animationDelay ? "animate-pulse" : ""}`}
      style={animationDelay ? { animationDelay: `${index * 100}ms` } : {}}
    >
      <Card className={`bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-0 ${cardClassName}`}>
        {showImage && (
          <Skeleton className="rounded-lg">
            <div className={`${imageHeight} rounded-lg bg-gray-200 dark:bg-gray-700`}></div>
          </Skeleton>
        )}
        <CardBody className={showImage ? "pt-4" : "p-4"}>
          {showTitle && (
            <Skeleton className="mb-3 rounded-lg">
              <div className={`h-6 ${titleWidth} rounded-lg bg-gray-200 dark:bg-gray-700`}></div>
            </Skeleton>
          )}
          {showSubtitle && (
            <Skeleton className="rounded-lg">
              <div className={`h-4 ${subtitleWidth} rounded-lg bg-gray-200 dark:bg-gray-700`}></div>
            </Skeleton>
          )}
          {showBody && (
            <div className="space-y-2 mt-3">
              {Array.from({ length: bodyLines }).map((_, lineIndex) => (
                <Skeleton key={lineIndex} className="rounded-lg">
                  <div
                    className={`h-3 ${lineIndex === bodyLines - 1 ? "w-2/3" : "w-full"} rounded-lg bg-gray-200 dark:bg-gray-700`}
                  ></div>
                </Skeleton>
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );

  const renderListSkeleton = (index: number) => (
    <div
      key={index}
      className={`w-full ${animationDelay ? "animate-pulse" : ""}`}
      style={animationDelay ? { animationDelay: `${index * 50}ms` } : {}}
    >
      <Card className={`bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-0 ${cardClassName}`}>
        <CardBody className="p-4">
          <div className="flex items-center gap-4">
            {showImage && (
              <Skeleton className="rounded-lg">
                <div className="h-16 w-16 rounded-lg bg-gray-200 dark:bg-gray-700"></div>
              </Skeleton>
            )}
            <div className="flex-1 space-y-2">
              {showTitle && (
                <Skeleton className="rounded-lg">
                  <div className={`h-5 ${titleWidth} rounded-lg bg-gray-200 dark:bg-gray-700`}></div>
                </Skeleton>
              )}
              {showSubtitle && (
                <Skeleton className="rounded-lg">
                  <div className={`h-4 ${subtitleWidth} rounded-lg bg-gray-200 dark:bg-gray-700`}></div>
                </Skeleton>
              )}
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );

  const renderCustomSkeleton = (index: number) => (
    <div
      key={index}
      className={`${animationDelay ? "animate-pulse" : ""}`}
      style={animationDelay ? { animationDelay: `${index * 100}ms` } : {}}
    >
      {children}
    </div>
  );

  const renderSkeleton = (index: number) => {
    switch (variant) {
      case "list":
        return renderListSkeleton(index);
      case "custom":
        return renderCustomSkeleton(index);
      case "card":
      default:
        return renderCardSkeleton(index);
    }
  };

  const containerClasses = [
    "grid gap-6",
    variant === "list" ? "grid-cols-1 gap-4" : GRID_CLASSES[gridCols],
    variant === "card" && gridCols >= 4 ? "justify-items-center" : "",
    className,
  ]
    .filter(Boolean)
    .join(" ");

  return (
    <div className={containerClasses}>{Array.from({ length: count }).map((_, index) => renderSkeleton(index))}</div>
  );
}

// Convenience wrapper for resume card skeletons
export function ResumeCardSkeleton({ count = 6, className = "" }: Pick<SkeletonLoaderProps, "count" | "className">) {
  return (
    <SkeletonLoader
      count={count}
      gridCols={5}
      variant="card"
      showImage={true}
      imageHeight="h-[320px]"
      showTitle={true}
      titleWidth="w-3/4"
      showSubtitle={true}
      subtitleWidth="w-1/2"
      className={className}
    />
  );
}

// Convenience wrapper for website card skeletons
export function WebsiteCardSkeleton({ count = 6, className = "" }: Pick<SkeletonLoaderProps, "count" | "className">) {
  return (
    <SkeletonLoader
      count={count}
      gridCols={3}
      variant="card"
      showImage={false}
      showTitle={true}
      titleWidth="w-2/3"
      showSubtitle={true}
      subtitleWidth="w-1/2"
      showBody={true}
      bodyLines={2}
      className={className}
    />
  );
}

// Convenience wrapper for list item skeletons
export function ListItemSkeleton({ count = 5, className = "" }: Pick<SkeletonLoaderProps, "count" | "className">) {
  return (
    <SkeletonLoader
      count={count}
      variant="list"
      showImage={true}
      showTitle={true}
      titleWidth="w-1/2"
      showSubtitle={true}
      subtitleWidth="w-1/3"
      className={className}
    />
  );
}
