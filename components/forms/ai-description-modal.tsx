"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  Select,
  SelectItem,
  Spinner,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";
import { aiService, type ContextType } from "@/lib/ai-service";

interface AIDescriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: (description: string) => void;
  contextType: ContextType;
  resumeContext: any;
  itemContext: any;
  currentDescription?: string;
  language?: "en" | "ar";
}

interface AIOptions {
  tone: "professional" | "casual" | "technical";
  length: "short" | "medium" | "detailed";
}

export function AIDescriptionModal({
  isOpen,
  onClose,
  onAccept,
  contextType,
  resumeContext,
  itemContext,
  currentDescription,
  language = "en",
}: AIDescriptionModalProps) {
  const t = useTranslations();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedDescriptions, setGeneratedDescriptions] = useState<string[]>([]);
  const [selectedDescription, setSelectedDescription] = useState<string>("");
  const [options, setOptions] = useState<AIOptions>({
    tone: "professional",
    length: "medium",
  });
  const [error, setError] = useState<string>("");

  const handleGenerate = useCallback(async () => {
    setIsGenerating(true);
    setError("");
    setGeneratedDescriptions([]);
    setSelectedDescription("");

    try {
      // Generate multiple variations
      const variations = await Promise.all([
        aiService.generateDescription(
          contextType,
          resumeContext,
          { ...itemContext, currentDescription },
          { ...options, language },
        ),
        aiService.generateDescription(
          contextType,
          resumeContext,
          { ...itemContext, currentDescription },
          { ...options, language },
        ),
        aiService.generateDescription(
          contextType,
          resumeContext,
          { ...itemContext, currentDescription },
          { ...options, language },
        ),
      ]);

      const successfulDescriptions = variations
        .filter((result) => result.success && result.description)
        .map((result) => result.description!);

      if (successfulDescriptions.length === 0) {
        setError(t("ai.generation_failed"));
        return;
      }

      setGeneratedDescriptions(successfulDescriptions);
      setSelectedDescription(successfulDescriptions[0]);
    } catch (err) {
      console.error("AI generation error:", err);
      setError(t("ai.generation_error"));
    } finally {
      setIsGenerating(false);
    }
  }, [contextType, resumeContext, itemContext, currentDescription, options, language, t]);

  const handleAccept = useCallback(() => {
    if (selectedDescription) {
      onAccept(selectedDescription);
      onClose();
    }
  }, [selectedDescription, onAccept, onClose]);

  const handleClose = useCallback(() => {
    setGeneratedDescriptions([]);
    setSelectedDescription("");
    setError("");
    onClose();
  }, [onClose]);

  const toneOptions = [
    { key: "professional", label: t("ai.tone.professional") },
    { key: "casual", label: t("ai.tone.casual") },
    { key: "technical", label: t("ai.tone.technical") },
  ];

  const lengthOptions = [
    { key: "short", label: t("ai.length.short") },
    { key: "medium", label: t("ai.length.medium") },
    { key: "detailed", label: t("ai.length.detailed") },
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size="3xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
        footer: "flex-shrink-0",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex items-center gap-2">
          <Icon icon="mdi:magic-staff" className="w-5 h-5 text-primary" />
          <span>{t("ai.write_with_ai")}</span>
        </ModalHeader>

        <ModalBody>
          <div className="space-y-6">
            {/* Options Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label={t("ai.tone_label")}
                selectedKeys={[options.tone]}
                onSelectionChange={(keys) => {
                  const tone = Array.from(keys)[0] as AIOptions["tone"];
                  setOptions((prev) => ({ ...prev, tone }));
                }}
                variant="bordered"
              >
                {toneOptions.map((option) => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>

              <Select
                label={t("ai.length_label")}
                selectedKeys={[options.length]}
                onSelectionChange={(keys) => {
                  const length = Array.from(keys)[0] as AIOptions["length"];
                  setOptions((prev) => ({ ...prev, length }));
                }}
                variant="bordered"
              >
                {lengthOptions.map((option) => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>
            </div>

            {/* Generate Button */}
            <div className="flex justify-center">
              <Button
                color="primary"
                variant="solid"
                startContent={isGenerating ? <Spinner size="sm" color="white" /> : <Icon icon="mdi:magic-staff" />}
                isDisabled={isGenerating}
                onPress={handleGenerate}
                size="lg"
              >
                {isGenerating ? t("ai.generating") : t("ai.generate_description")}
              </Button>
            </div>

            {/* Error Display */}
            {error && (
              <div className="bg-danger-50 border border-danger-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-danger-700">
                  <Icon icon="mdi:alert-circle" />
                  <span className="font-medium">{t("common.error")}</span>
                </div>
                <p className="text-danger-600 mt-1">{error}</p>
              </div>
            )}

            {/* Generated Descriptions */}
            {generatedDescriptions.length > 0 && (
              <div className="space-y-4">
                <h4 className="text-lg font-semibold">{t("ai.generated_options")}</h4>
                <div className="space-y-3">
                  {generatedDescriptions.map((description, index) => (
                    <div
                      key={index}
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedDescription === description
                          ? "border-primary bg-primary-50 dark:bg-primary-900/20"
                          : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                      }`}
                      onClick={() => setSelectedDescription(description)}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-1">
                          <div
                            className={`w-4 h-4 rounded-full border-2 ${
                              selectedDescription === description
                                ? "border-primary bg-primary"
                                : "border-gray-300 dark:border-gray-600"
                            }`}
                          >
                            {selectedDescription === description && (
                              <div className="w-full h-full rounded-full bg-white scale-50"></div>
                            )}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div
                            className="prose prose-sm dark:prose-invert max-w-none"
                            dangerouslySetInnerHTML={{ __html: description }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Current Description (if exists) */}
            {currentDescription && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                  {t("ai.current_description")}
                </h4>
                <div
                  className="prose prose-sm dark:prose-invert max-w-none p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  dangerouslySetInnerHTML={{ __html: currentDescription }}
                />
              </div>
            )}
          </div>
        </ModalBody>

        <ModalFooter>
          <Button variant="light" onPress={handleClose}>
            {t("common.cancel")}
          </Button>
          <Button
            color="primary"
            onPress={handleAccept}
            isDisabled={!selectedDescription}
            startContent={<Icon icon="mdi:check" />}
          >
            {t("ai.use_description")}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
