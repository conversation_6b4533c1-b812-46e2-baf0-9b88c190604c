"use client";

import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ody,
  CardHeader,
  <PERSON>,
  Divider,
  <PERSON>dal,
  ModalBody,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import LoaderComponent from "@/components/shared/common/LoaderComponent";
import { useCurrentUser } from "@/hooks/use-current-user";

export function BillingPage() {
  const t = useTranslations("billing");
  const tCommon = useTranslations("common");
  const { user, isPremium, isLoading } = useCurrentUser();
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [isCancelling, setIsCancelling] = useState(false);

  // Fetch real invoice data from Paddle
  const { data: invoices = [], isLoading: invoicesLoading, error: invoicesError } = trpc.user.getInvoices.useQuery();

  const handleCancelSubscription = async () => {
    setIsCancelling(true);
    try {
      // TODO: Implement subscription cancellation API call
      console.log("Cancelling subscription...");

      // For now, just show a placeholder
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Refresh the page or update the state
      window.location.reload();
    } catch (error) {
      console.error("Failed to cancel subscription:", error);
    } finally {
      setIsCancelling(false);
    }
  };

  const handleDownloadInvoice = (invoice: any) => {
    if (invoice.downloadUrl) {
      // Open the Paddle receipt URL in a new window for download
      window.open(invoice.downloadUrl, "_blank");
    } else {
      toast.error(t("invoices.download_unavailable"));
    }
  };

  if (isLoading) {
    return <LoaderComponent />;
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="max-w-md">
          <CardBody className="text-center">
            <Icon icon="tabler:user-x" className="w-16 h-16 mx-auto mb-4 text-default-400" />
            <h2 className="text-xl font-semibold mb-2">{t("not_found_title")}</h2>
            <p className="text-default-600">{t("not_found_description")}</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t("title")}</h1>
          <p className="text-default-600 mt-1">{t("subtitle")}</p>
        </div>
      </div>

      {/* Subscription Status */}
      <Card>
        <CardHeader className="flex gap-3">
          <Icon icon="tabler:crown" className="w-6 h-6 text-amber-500" />
          <div className="flex flex-col">
            <p className="text-lg font-semibold">{t("subscription.title")}</p>
            <p className="text-small text-default-500">{t("subscription.description")}</p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <h2 className="text-xl font-semibold">
                  {isPremium ? t("subscription.premium_plan") : t("subscription.free_plan")}
                </h2>
                <Chip
                  size="sm"
                  variant="flat"
                  className={isPremium ? "bg-gradient-to-r from-amber-400 to-orange-500 text-white" : "bg-default-100"}
                >
                  {isPremium ? "✨ Premium" : "Free"}
                </Chip>
              </div>
              {isPremium && (
                <div className="space-y-1">
                  <p className="text-sm text-default-600">
                    {t("subscription.type")}: <span className="font-medium">{t("subscription.lifetime")}</span>
                  </p>
                  <p className="text-sm text-default-600">
                    {t("subscription.purchased")}: <span className="font-medium">January 15, 2024</span>
                  </p>
                  <p className="text-sm text-default-600">
                    {t("subscription.status")}:{" "}
                    <span className="font-medium text-success">{t("subscription.active")}</span>
                  </p>
                </div>
              )}
            </div>
            {isPremium && (
              <Button color="danger" variant="flat" size="sm" onPress={onOpen}>
                {t("subscription.revoke_access")}
              </Button>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Billing History */}
      <Card>
        <CardHeader className="flex gap-3">
          <Icon icon="tabler:receipt" className="w-6 h-6" />
          <div className="flex flex-col">
            <p className="text-lg font-semibold">{t("invoices.title")}</p>
            <p className="text-small text-default-500">{t("invoices.description")}</p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody>
          {invoicesLoading ? (
            <div className="flex items-center justify-center py-8">
              <Icon icon="tabler:loader-2" className="w-6 h-6 animate-spin mr-2" />
              <span>{tCommon("loading")}</span>
            </div>
          ) : invoicesError ? (
            <div className="text-center py-8">
              <Icon icon="tabler:alert-circle" className="w-16 h-16 mx-auto mb-4 text-danger" />
              <h3 className="text-lg font-semibold mb-2 text-danger">{t("invoices.error_title")}</h3>
              <p className="text-default-600">{t("invoices.error_description")}</p>
            </div>
          ) : invoices.length > 0 ? (
            <Table aria-label={t("invoices.table_aria_label")}>
              <TableHeader>
                <TableColumn>{t("invoices.invoice_id")}</TableColumn>
                <TableColumn>{t("invoices.date")}</TableColumn>
                <TableColumn>{t("invoices.description")}</TableColumn>
                <TableColumn>{t("invoices.amount")}</TableColumn>
                <TableColumn>{t("invoices.status")}</TableColumn>
                <TableColumn>{t("invoices.actions")}</TableColumn>
              </TableHeader>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-mono text-sm">{invoice.invoiceNumber || invoice.id}</TableCell>
                    <TableCell>{new Date(invoice.date).toLocaleDateString()}</TableCell>
                    <TableCell>{invoice.description}</TableCell>
                    <TableCell className="font-medium">{invoice.amount}</TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        variant="flat"
                        className={
                          invoice.status === "completed"
                            ? "bg-success-100 text-success-800"
                            : "bg-warning-100 text-warning-800"
                        }
                      >
                        {t(`invoices.status_${invoice.status === "completed" ? "paid" : invoice.status}`)}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        variant="flat"
                        startContent={<Icon icon="tabler:download" className="w-4 h-4" />}
                        onPress={() => handleDownloadInvoice(invoice)}
                      >
                        {t("invoices.download")}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <Icon icon="tabler:receipt-off" className="w-16 h-16 mx-auto mb-4 text-default-400" />
              <h3 className="text-lg font-semibold mb-2">{t("invoices.no_invoices_title")}</h3>
              <p className="text-default-600">{t("invoices.no_invoices_description")}</p>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Revoke Access Confirmation Modal */}
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="md">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <div className="flex items-center gap-2">
                  <Icon icon="tabler:alert-triangle" className="w-5 h-5 text-danger" />
                  {t("revoke.confirm_title")}
                </div>
              </ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <p className="text-default-600">{t("revoke.confirm_description")}</p>
                  <div className="bg-danger-50 border border-danger-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <Icon icon="tabler:alert-circle" className="w-5 h-5 text-danger mt-0.5" />
                      <div>
                        <h4 className="font-medium text-danger">{t("revoke.warning_title")}</h4>
                        <ul className="text-sm text-danger-600 mt-2 space-y-1">
                          <li>• {t("revoke.warning_immediate")}</li>
                          <li>• {t("revoke.warning_features")}</li>
                          <li>• {t("revoke.warning_permanent")}</li>
                          <li>• {t("revoke.warning_repurchase")}</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="flat" onPress={onClose}>
                  {tCommon("cancel")}
                </Button>
                <Button
                  color="danger"
                  onPress={() => {
                    onClose();
                    handleCancelSubscription();
                  }}
                  isLoading={isCancelling}
                >
                  {isCancelling ? t("revoke.revoking") : t("revoke.confirm_revoke")}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
}
