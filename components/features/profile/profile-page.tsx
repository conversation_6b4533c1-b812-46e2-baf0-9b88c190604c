"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ody,
  Card<PERSON><PERSON><PERSON>,
  <PERSON>,
  Divider,
  <PERSON>dal,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { useCurrentUser } from "@/hooks/use-current-user";

export function ProfilePage() {
  const t = useTranslations("profile");
  const tCommon = useTranslations("common");
  const router = useRouter();
  const { user, isPremium, isLoading } = useCurrentUser();
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [isDeleting, setIsDeleting] = useState(false);

  const deleteAccountMutation = trpc.user.deleteAccount.useMutation();

  const handleDeleteAccount = async () => {
    setIsDeleting(true);
    try {
      await deleteAccountMutation.mutateAsync();

      toast.success(t("danger.delete_success"));

      // After successful deletion, redirect to home
      setTimeout(() => {
        router.push("/");
      }, 1000);
    } catch (error) {
      console.error("Failed to delete account:", error);
      toast.error(t("danger.delete_error"));
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Icon icon="tabler:loader-2" className="w-8 h-8 animate-spin" />
        <span className="ml-2">{tCommon("loading")}</span>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="max-w-md">
          <CardBody className="text-center">
            <Icon icon="tabler:user-x" className="w-16 h-16 mx-auto mb-4 text-default-400" />
            <h2 className="text-xl font-semibold mb-2">{t("not_found_title")}</h2>
            <p className="text-default-600">{t("not_found_description")}</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t("title")}</h1>
          <p className="text-default-600 mt-1">{t("subtitle")}</p>
        </div>
      </div>

      {/* Profile Information Card */}
      <Card>
        <CardHeader className="flex gap-3">
          <Icon icon="tabler:user" className="w-6 h-6" />
          <div className="flex flex-col">
            <p className="text-lg font-semibold">{t("personal_info.title")}</p>
            <p className="text-small text-default-500">{t("personal_info.description")}</p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody className="space-y-6">
          <div className="flex items-center gap-6">
            <Avatar name={user.emailAddress} size="lg" className="w-24 h-24" />
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2">
                <h2 className="text-xl font-semibold">{user.emailAddress}</h2>
                {isPremium && (
                  <Chip size="sm" variant="flat" className="bg-gradient-to-r from-amber-400 to-orange-500 text-white">
                    ✨ Premium
                  </Chip>
                )}
              </div>
              <p className="text-default-600">{t("member_since", { date: new Date().toLocaleDateString() })}</p>
            </div>
          </div>

          <Divider />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Icon icon="tabler:mail" className="w-5 h-5" />
                {t("contact_info.title")}
              </h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Icon icon="tabler:mail" className="w-4 h-4 text-default-400" />
                  <span className="text-sm">{user.emailAddress}</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Icon icon="tabler:crown" className="w-5 h-5 text-amber-500" />
                {t("subscription.title")}
              </h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Icon
                    icon={isPremium ? "tabler:check" : "tabler:x"}
                    className={`w-4 h-4 ${isPremium ? "text-success" : "text-default-400"}`}
                  />
                  <span className="text-sm">{isPremium ? t("subscription.premium") : t("subscription.free")}</span>
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Account Statistics Card */}
      <Card>
        <CardHeader className="flex gap-3">
          <Icon icon="tabler:chart-line" className="w-6 h-6" />
          <div className="flex flex-col">
            <p className="text-lg font-semibold">{t("statistics.title")}</p>
            <p className="text-small text-default-500">{t("statistics.description")}</p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center">
                <Icon icon="tabler:file-text" className="w-8 h-8 text-primary" />
              </div>
              <div>
                <p className="text-2xl font-bold">0</p>
                <p className="text-sm text-default-600">{t("statistics.resumes")}</p>
              </div>
            </div>
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center">
                <Icon icon="tabler:world" className="w-8 h-8 text-success" />
              </div>
              <div>
                <p className="text-2xl font-bold">0</p>
                <p className="text-sm text-default-600">{t("statistics.websites")}</p>
              </div>
            </div>
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center">
                <Icon icon="tabler:calendar" className="w-8 h-8 text-warning" />
              </div>
              <div>
                <p className="text-2xl font-bold">1</p>
                <p className="text-sm text-default-600">{t("statistics.days_active")}</p>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Danger Zone */}
      <Card className="border-danger-200">
        <CardHeader className="flex gap-3">
          <Icon icon="tabler:alert-triangle" className="w-6 h-6 text-danger" />
          <div className="flex flex-col">
            <p className="text-lg font-semibold text-danger">{t("danger.title")}</p>
            <p className="text-small text-default-500">{t("danger.description")}</p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-danger">{t("danger.delete_account")}</h3>
              <p className="text-sm text-default-600">{t("danger.delete_account_description")}</p>
            </div>
            <Button color="danger" variant="flat" size="sm" onPress={onOpen}>
              {t("danger.delete_account")}
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Delete Account Confirmation Modal */}
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="md">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <div className="flex items-center gap-2">
                  <Icon icon="tabler:alert-triangle" className="w-5 h-5 text-danger" />
                  {t("danger.confirm_title")}
                </div>
              </ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <p className="text-default-600">{t("danger.confirm_description")}</p>
                  <div className="bg-danger-50 border border-danger-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <Icon icon="tabler:alert-circle" className="w-5 h-5 text-danger mt-0.5" />
                      <div>
                        <h4 className="font-medium text-danger">{t("danger.warning_title")}</h4>
                        <ul className="text-sm text-danger-600 mt-2 space-y-1">
                          <li>• {t("danger.warning_resumes")}</li>
                          <li>• {t("danger.warning_websites")}</li>
                          <li>• {t("danger.warning_data")}</li>
                          <li>• {t("danger.warning_irreversible")}</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="flat" onPress={onClose}>
                  {tCommon("cancel")}
                </Button>
                <Button
                  color="danger"
                  onPress={() => {
                    onClose();
                    handleDeleteAccount();
                  }}
                  isLoading={isDeleting}
                >
                  {isDeleting ? t("danger.deleting") : t("danger.confirm_delete")}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
}
