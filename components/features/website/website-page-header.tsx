"use client";

import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/shared/page-header";
import { SortOption } from "@/hooks/use-filter-sort";

interface WebsitePageHeaderProps {
  websiteCount: number;
  onFilterChange?: (filter: string) => void;
  onSortChange?: (sort: SortOption) => void;
  onExportAll?: () => void;
  onReset?: () => void;
  currentFilter?: string;
  currentSort?: SortOption;
  filteredCount?: number;
  showCreateButton?: boolean;
}

const WEBSITE_FILTER_OPTIONS = [
  { key: "all", icon: "tabler:list", labelKey: "all_websites" },
  { key: "published", icon: "tabler:world", labelKey: "published" },
  { key: "unpublished", icon: "tabler:eye-off", labelKey: "unpublished" },
  { key: "recent", icon: "tabler:clock", labelKey: "recent" },
];

export default function WebsitePageHeader({
  websiteCount,
  onFilterChange,
  onSortChange,
  onExportAll,
  onReset,
  currentFilter,
  currentSort,
  filteredCount,
  showCreateButton = true,
}: WebsitePageHeaderProps) {
  const t = useTranslations("websites");

  // Translate filter options
  const translatedFilterOptions = WEBSITE_FILTER_OPTIONS.map((option) => ({
    key: option.key,
    icon: option.icon,
    label: t(option.labelKey),
  }));

  const primaryAction =
    showCreateButton && websiteCount > 0 ? (
      <Button
        as={Link}
        href="/websites/builder"
        color="primary"
        size="lg"
        className="bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-200"
        startContent={<Icon icon="tabler:plus" className="w-5 h-5" />}
      >
        {t("create_website")}
      </Button>
    ) : undefined;

  return (
    <PageHeader
      icon="tabler:world-www"
      titleKey="page_title"
      subtitleKey="page_subtitle"
      itemCount={websiteCount}
      translationNamespace="websites"
      primaryAction={primaryAction}
      onExportAll={onExportAll}
      onFilterChange={onFilterChange}
      onSortChange={onSortChange}
      onReset={onReset}
      currentFilter={currentFilter}
      currentSort={currentSort}
      filteredCount={filteredCount}
      filterOptions={translatedFilterOptions}
      exportDisabled={websiteCount === 0}
      itemType="websites"
    />
  );
}
