import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import React from "react";
import { GradientEmptyState } from "@/components/shared/empty-state";

const EmptyResumesState = () => {
  const t = useTranslations("website");
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
      <div className="max-w-2xl mx-auto px-4">
        <div className="text-center py-16">
          <GradientEmptyState
            icon="heroicons:document-text"
            title={t("builder.no_resumes_title")}
            description={t("builder.no_resumes_subtitle")}
            actionButton={
              <Button
                as={Link}
                href="/resumes"
                color="primary"
                size="lg"
                className="bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg hover:shadow-xl transition-shadow"
                startContent={<Icon icon="heroicons:plus" />}
              >
                {t("builder.create_resume_first")}
              </Button>
            }
          />
        </div>
      </div>
    </div>
  );
};

export default EmptyResumesState;
