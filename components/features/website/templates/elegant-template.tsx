import { useTranslations } from "next-intl";
import React from "react";
import { WebsiteFooter, WebsiteNav, WebsiteSection, WebsiteTemplateProps } from "@/components";
import { formatLocation, getFullName } from "@/components/features/resume/templates/shared/utils";

export const ElegantTemplate: React.FC<WebsiteTemplateProps> = ({ resume, website, className = "" }) => {
  const t = useTranslations("website.sections");
  const fullName = getFullName(resume.firstName || "", resume.lastName || "");
  const location = formatLocation(resume.city || "", resume.country || "");

  // Navigation sections
  const sections = [
    { id: "about", label: t("about") },
    { id: "experience", label: t("experience") },
    { id: "projects", label: t("projects") },
    { id: "skills", label: t("skills") },
    { id: "education", label: t("education") },
    { id: "contact", label: t("contact") },
  ];

  return (
    <div
      className={`elegant-template min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 ${className}`}
    >
      {/* Navigation */}
      <WebsiteNav
        fullName={fullName}
        sections={sections}
        className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-white/20"
      />

      {/* Hero Section with Enhanced Design */}
      <section className="relative py-20 px-4 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 start-10 w-32 h-32 bg-blue-400 rounded-full mix-blend-multiply animate-pulse"></div>
          <div className="absolute top-40 end-20 w-24 h-24 bg-indigo-400 rounded-full mix-blend-multiply animate-bounce"></div>
          <div className="absolute bottom-20 start-1/3 w-20 h-20 bg-purple-400 rounded-full mix-blend-multiply animate-pulse"></div>
        </div>

        <div className="relative z-10 max-w-5xl mx-auto text-center">
          {resume.showPhoto === 1 && resume.photo && (
            <div className="mb-8">
              <div className="relative inline-block">
                <img
                  alt={fullName}
                  className="w-40 h-40 rounded-full object-cover mx-auto shadow-2xl ring-8 ring-white/50"
                  src={resume.photo}
                />
                <div className="absolute inset-0 rounded-full bg-gradient-to-t from-blue-500/20 to-transparent"></div>
              </div>
            </div>
          )}

          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-4 tracking-tight">{fullName}</h1>

          <p className="text-2xl md:text-3xl text-blue-700 mb-8 font-light">{resume.jobTitle}</p>

          {resume.bio && (
            <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 max-w-4xl mx-auto">
              <div dangerouslySetInnerHTML={{ __html: resume.bio }} className="text-lg text-gray-700 leading-relaxed" />
            </div>
          )}

          {location && (
            <div className="mt-6 text-gray-600 flex items-center justify-center gap-2">
              <span className="text-blue-500">📍</span>
              <span className="font-medium">{location}</span>
            </div>
          )}
        </div>
      </section>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-16">
        {/* About Section */}
        <WebsiteSection title={t("about")} id="about" className="mb-16">
          <div className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/20">
            <div className="text-lg text-gray-700 leading-relaxed">
              {resume.bio ? (
                <div dangerouslySetInnerHTML={{ __html: resume.bio }} />
              ) : (
                <p>Professional with expertise in {resume.jobTitle}.</p>
              )}
            </div>
            {location && (
              <div className="mt-6 flex items-center text-blue-600 font-medium">
                <span className="me-2 text-xl">📍</span>
                <span>{location}</span>
              </div>
            )}
          </div>
        </WebsiteSection>

        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <WebsiteSection title={t("experience")} id="experience" className="mb-16">
            <div className="space-y-6">
              {resume.experiences.map((experience) => (
                <div
                  key={experience.id}
                  className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start mb-4">
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">{experience.title}</h3>
                      <p className="text-xl text-blue-700 font-semibold mb-2">{experience.company}</p>
                      {(experience.city || experience.country) && (
                        <p className="text-gray-600 flex items-center gap-2">
                          <span className="text-blue-500">📍</span>
                          <span>{[experience.city, experience.country].filter(Boolean).join(", ")}</span>
                        </p>
                      )}
                    </div>
                    <div className="mt-4 lg:mt-0">
                      <div className="bg-blue-100/50 backdrop-blur-sm px-4 py-2 rounded-xl border border-blue-200/50">
                        <p className="font-semibold text-blue-800">
                          {experience.startDate && experience.endDate
                            ? `${new Date(experience.startDate).getFullYear()} - ${
                                experience.isCurrent ? "Present" : new Date(experience.endDate).getFullYear()
                              }`
                            : ""}
                        </p>
                      </div>
                    </div>
                  </div>
                  {experience.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: experience.description,
                      }}
                      className="text-gray-700 leading-relaxed prose prose-lg max-w-none"
                    />
                  )}
                </div>
              ))}
            </div>
          </WebsiteSection>
        )}

        {/* Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <WebsiteSection title={t("projects")} id="projects" className="mb-16">
            <div className="grid md:grid-cols-2 gap-6">
              {resume.projects.map((project) => (
                <div
                  key={project.id}
                  className="bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
                >
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{project.title}</h3>
                    {project.client && <p className="text-blue-700 font-semibold mb-2">{project.client}</p>}
                    {(project.startDate || project.endDate) && (
                      <p className="text-gray-600 text-sm">
                        {project.startDate && project.endDate
                          ? `${new Date(project.startDate).getFullYear()} - ${new Date(project.endDate).getFullYear()}`
                          : project.startDate
                            ? new Date(project.startDate).getFullYear()
                            : project.endDate
                              ? new Date(project.endDate).getFullYear()
                              : ""}
                      </p>
                    )}
                  </div>
                  {project.description && (
                    <div
                      dangerouslySetInnerHTML={{ __html: project.description }}
                      className="text-gray-700 leading-relaxed mb-4"
                    />
                  )}
                  {project.url && (
                    <a
                      href={project.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors font-medium"
                    >
                      <span className="me-2">🔗</span>
                      View Project
                    </a>
                  )}
                </div>
              ))}
            </div>
          </WebsiteSection>
        )}

        {/* Skills Section */}
        {resume.skills && resume.skills.length > 0 && (
          <WebsiteSection title={t("skills")} id="skills" className="mb-16">
            <div className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/20">
              {(() => {
                const skillsByCategory = resume.skills.reduce(
                  (acc, skill) => {
                    const category = skill.category || "Other";
                    if (!acc[category]) acc[category] = [];
                    acc[category].push(skill);
                    return acc;
                  },
                  {} as Record<string, typeof resume.skills>,
                );

                return (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
                      <div key={category}>
                        <h3 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-blue-200">
                          {category}
                        </h3>
                        <div className="space-y-3">
                          {categorySkills.map((skill) => (
                            <div key={skill.id} className="flex items-center justify-between">
                              <span className="text-gray-700 font-medium">{skill.name}</span>
                              {skill.proficiency !== undefined && skill.proficiency !== null ? (
                                <div className="flex items-center gap-2">
                                  <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                                    <div
                                      className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all"
                                      style={{
                                        width: `${skill.proficiency}%`,
                                      }}
                                    />
                                  </div>
                                  <span className="text-xs text-gray-500 w-8 text-end">{skill.proficiency}%</span>
                                </div>
                              ) : null}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                );
              })()}
            </div>
          </WebsiteSection>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <WebsiteSection title={t("education")} id="education" className="mb-16">
            <div className="space-y-6">
              {resume.educations.map((education) => (
                <div
                  key={education.id}
                  className="bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start">
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{education.institution}</h3>
                      <p className="text-lg text-blue-700 font-semibold mb-2">
                        {education.fieldOfStudy ? `${education.degree} in ${education.fieldOfStudy}` : education.degree}
                      </p>
                      {(education.city || education.country) && (
                        <p className="text-gray-600 flex items-center gap-2">
                          <span className="text-blue-500">📍</span>
                          <span>{[education.city, education.country].filter(Boolean).join(", ")}</span>
                        </p>
                      )}
                    </div>
                    <div className="mt-4 lg:mt-0">
                      <div className="bg-indigo-100/50 backdrop-blur-sm px-4 py-2 rounded-xl border border-indigo-200/50">
                        <p className="font-semibold text-indigo-800">
                          {education.startDate && education.endDate
                            ? `${new Date(education.startDate).getFullYear()} - ${
                                education.isCurrent ? "Present" : new Date(education.endDate).getFullYear()
                              }`
                            : ""}
                        </p>
                      </div>
                    </div>
                  </div>
                  {education.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: education.description,
                      }}
                      className="text-gray-700 leading-relaxed mt-4"
                    />
                  )}
                </div>
              ))}
            </div>
          </WebsiteSection>
        )}

        {/* Contact Section */}
        <WebsiteSection title={t("contact")} id="contact">
          <div className="bg-gradient-to-br from-blue-500/90 to-purple-600/90 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/20 text-white">
            <div className="text-center max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold mb-6">Let's Connect</h3>
              <div className="space-y-4">
                {resume.email && (
                  <div className="flex items-center justify-center gap-3">
                    <span className="text-2xl">✉️</span>
                    <a
                      href={`mailto:${resume.email}`}
                      className="text-lg hover:text-blue-200 transition-colors font-medium"
                    >
                      {resume.email}
                    </a>
                  </div>
                )}
                {resume.website && (
                  <div className="flex items-center justify-center gap-3">
                    <span className="text-2xl">🌐</span>
                    <a
                      href={resume.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-lg hover:text-blue-200 transition-colors font-medium"
                    >
                      {resume.website}
                    </a>
                  </div>
                )}
                {location && (
                  <div className="flex items-center justify-center gap-3">
                    <span className="text-2xl">📍</span>
                    <span className="text-lg font-medium">{location}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </WebsiteSection>
      </main>

      {/* Footer */}
      <WebsiteFooter fullName={fullName} />
    </div>
  );
};
