"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";
import React from "react";
import { WebsiteContact, WebsiteEducation, WebsiteSection, WebsiteTemplateProps } from "@/components";
import { formatDateRange, formatLocation, getFullName } from "@/components/features/resume/templates/shared/utils";

export const ExecutiveTemplate: React.FC<WebsiteTemplateProps> = ({ resume, website, className = "" }) => {
  const t = useTranslations("website.sections");
  const fullName = getFullName(resume.firstName, resume.lastName);
  const displayTitle = resume.jobTitle;
  const displayBio = resume.bio;
  const location = formatLocation(resume.city || "", resume.country || "");

  // Filter and sort experiences for executives (focus on leadership roles)
  const experiences =
    resume.experiences?.filter((exp) => exp.title && exp.company).sort((a, b) => (b.sort || 0) - (a.sort || 0)) || [];

  // Filter significant achievements from awards and certifications
  const achievements = [...(resume.awards || []), ...(resume.certifications || [])]
    .filter((item) => item.title)
    .sort((a, b) => {
      // Sort by date if available, otherwise by sort order
      const aDate = new Date(a.dateReceived || a.dateReceived || 0);
      const bDate = new Date(b.dateReceived || b.dateReceived || 0);
      return bDate.getTime() - aDate.getTime();
    })
    .slice(0, 6); // Show only top 6 achievements

  // Education sorted by completion date
  const educations =
    resume.educations
      ?.filter((edu) => edu.institution && edu.degree)
      .sort((a, b) => {
        const aDate = new Date(a.endDate || a.endDate || 0);
        const bDate = new Date(b.endDate || b.endDate || 0);
        return bDate.getTime() - aDate.getTime();
      }) || [];

  // Skills grouped and filtered for executive level
  const executiveSkills =
    resume.skills
      ?.filter((skill) => skill.name && (skill.proficiency || 0) >= 70)
      .reduce(
        (acc, skill) => {
          const category = skill.category || "Leadership";
          if (!acc[category]) acc[category] = [];
          acc[category].push(skill);
          return acc;
        },
        {} as Record<string, typeof resume.skills>,
      ) || {};

  const navSections = [
    { id: "about", label: t("about") },
    { id: "contact", label: t("contact") },
    ...(experiences.length > 0 ? [{ id: "experience", label: t("experience") }] : []),
    ...(achievements.length > 0 ? [{ id: "achievements", label: "Achievements" }] : []),
    ...(Object.keys(executiveSkills).length > 0 ? [{ id: "expertise", label: "Expertise" }] : []),
    ...(educations.length > 0 ? [{ id: "education", label: t("education") }] : []),
  ];

  return (
    <div
      className={`executive-template bg-gradient-to-br from-slate-50 via-gray-50 to-blue-50 min-h-screen ${className}`}
    >
      {/* Navigation Bar */}
      <nav className="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-5">
          <div className="flex justify-between items-center">
            <div className="font-bold text-2xl text-slate-900 tracking-tight">{fullName}</div>
            <div className="hidden md:flex space-x-10">
              {navSections.map((section) => (
                <a
                  key={section.id}
                  href={`#${section.id}`}
                  className="text-slate-600 hover:text-slate-900 transition-all duration-200 font-semibold text-sm uppercase tracking-wider border-b-2 border-transparent hover:border-slate-900 pb-1"
                >
                  {section.label}
                </a>
              ))}
            </div>
          </div>
        </div>
      </nav>

      {/* Executive Hero Section */}
      <section id="about" className="relative py-24 px-6 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 start-20 w-96 h-96 bg-blue-600 rounded-full mix-blend-multiply animate-pulse"></div>
          <div className="absolute bottom-20 end-20 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply animate-pulse"></div>
          <div className="absolute top-1/2 start-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-purple-600 rounded-full mix-blend-multiply animate-bounce"></div>
        </div>

        <div className="relative z-10 max-w-5xl mx-auto text-center">
          {resume.showPhoto && resume.photo ? (
            <div className="mb-12">
              <div className="relative inline-block">
                <Image
                  alt={fullName}
                  className="w-56 h-56 rounded-full object-cover mx-auto shadow-2xl ring-8 ring-white/50"
                  height={224}
                  src={resume.photo}
                  width={224}
                  priority
                />
                <div className="absolute inset-0 rounded-full bg-gradient-to-t from-slate-900/10 to-transparent"></div>
                {/* Executive ring effect */}
                <div className="absolute -inset-4 rounded-full border-2 border-slate-300/30 animate-pulse"></div>
              </div>
            </div>
          ) : null}

          <h1 className="text-6xl md:text-7xl font-bold text-slate-900 mb-6 tracking-tight">{fullName}</h1>

          {displayTitle && (
            <p className="text-3xl md:text-4xl text-slate-700 mb-8 font-light tracking-wide">{displayTitle}</p>
          )}

          {location && (
            <p className="text-xl text-slate-600 mb-12 flex items-center justify-center gap-3">
              <span className="text-2xl">📍</span>
              <span className="font-medium">{location}</span>
            </p>
          )}

          {displayBio && (
            <div className="bg-white/90 backdrop-blur-md rounded-3xl p-10 shadow-2xl border border-white/50 max-w-4xl mx-auto">
              <div
                dangerouslySetInnerHTML={{ __html: displayBio }}
                className="text-xl text-slate-700 leading-relaxed"
              />
            </div>
          )}
        </div>
      </section>

      {/* Professional Experience */}
      {experiences.length > 0 && (
        <WebsiteSection id="experience" title={t("experience")} className="max-w-7xl mx-auto px-6">
          <div className="space-y-10">
            {experiences.map((experience) => {
              const location = formatLocation(experience.city || "", experience.country || "");
              const dateRange = formatDateRange(experience.startDate, experience.endDate, experience.isCurrent);

              return (
                <div
                  key={experience.id}
                  className="bg-white/90 backdrop-blur-sm rounded-3xl p-10 shadow-2xl border border-white/20 hover:shadow-3xl transition-all duration-500 hover:scale-[1.01]"
                >
                  <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start mb-8">
                    <div className="flex-1">
                      <h3 className="text-3xl font-bold text-slate-900 mb-3">{experience.title}</h3>
                      <p className="text-2xl text-blue-800 font-bold mb-3">{experience.company}</p>
                      {location && (
                        <p className="text-slate-600 flex items-center gap-3 text-lg">
                          <span className="text-blue-600">📍</span>
                          <span className="font-medium">{location}</span>
                        </p>
                      )}
                    </div>
                    <div className="mt-6 lg:mt-0 lg:text-end">
                      <div className="bg-gradient-to-r from-slate-100 to-blue-50 px-6 py-3 rounded-2xl border border-slate-200">
                        <p className="font-bold text-slate-800 text-lg">{dateRange}</p>
                      </div>
                    </div>
                  </div>
                  {experience.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: experience.description,
                      }}
                      className="text-slate-700 leading-relaxed prose prose-slate prose-lg max-w-none"
                    />
                  )}
                </div>
              );
            })}
          </div>
        </WebsiteSection>
      )}

      {/* Key Achievements */}
      {achievements.length > 0 && (
        <WebsiteSection
          id="achievements"
          title="Key Achievements"
          className="max-w-7xl mx-auto px-6 bg-gradient-to-r from-slate-50/80 to-blue-50/80 py-20 rounded-3xl backdrop-blur-sm"
        >
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {achievements.map((achievement) => {
              const date = achievement.dateReceived || achievement.dateReceived;
              const formattedDate = date
                ? new Date(date).toLocaleDateString(undefined, {
                    year: "numeric",
                    month: "long",
                  })
                : null;

              return (
                <div
                  key={achievement.id}
                  className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/30 hover:shadow-2xl transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-start gap-4 mb-6">
                    <div className="w-14 h-14 bg-gradient-to-br from-amber-400 to-amber-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                      <span className="text-white text-2xl">🏆</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-bold text-slate-900 text-xl leading-tight">{achievement.title}</h4>
                      {achievement.issuer && (
                        <p className="text-blue-700 font-semibold text-base mt-2">{achievement.issuer}</p>
                      )}
                    </div>
                  </div>

                  {formattedDate && <p className="text-slate-600 text-sm mb-4 font-medium">{formattedDate}</p>}

                  {achievement.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: achievement.description,
                      }}
                      className="text-slate-700 text-base leading-relaxed mb-4"
                    />
                  )}

                  {achievement.url && (
                    <a
                      href={achievement.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors text-base font-medium mt-2"
                    >
                      <span className="me-2">🔗</span>
                      View Details
                    </a>
                  )}
                </div>
              );
            })}
          </div>
        </WebsiteSection>
      )}

      {/* Core Expertise */}
      {Object.keys(executiveSkills).length > 0 && (
        <WebsiteSection id="expertise" title="Core Expertise" className="max-w-7xl mx-auto px-6">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Object.entries(executiveSkills).map(([category, categorySkills]) => (
              <div
                key={category}
                className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300"
              >
                <h3 className="text-xl font-bold text-slate-900 mb-4 pb-2 border-b border-slate-200">{category}</h3>
                <div className="space-y-3">
                  {categorySkills.map((skill) => (
                    <div key={skill.id} className="flex items-center justify-between">
                      <span className="text-slate-700 font-medium">{skill.name}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-slate-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all"
                            style={{ width: `${skill.proficiency || 0}%` }}
                          />
                        </div>
                        <span className="text-xs text-slate-500 w-8 text-end">{skill.proficiency || 0}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </WebsiteSection>
      )}

      {/* Education */}
      {educations.length > 0 && (
        <WebsiteSection
          id="education"
          title={t("education")}
          className="max-w-6xl mx-auto px-6 bg-slate-50/50 py-16 rounded-3xl"
        >
          <WebsiteEducation educations={educations} />
        </WebsiteSection>
      )}

      {/* Contact */}
      <section id="contact" className="py-16 px-6 bg-slate-900 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-8">Get In Touch</h2>
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
            <WebsiteContact email={resume.email} website={resume.website} location={location} className="text-white" />
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-800 text-white py-8 text-center">
        <div className="max-w-6xl mx-auto px-6">
          <p className="text-slate-300 mb-2">
            © {new Date().getFullYear()} {fullName}
          </p>
          <p className="text-slate-400 text-sm">
            Powered by{" "}
            <a
              href="https://quickcv.com"
              className="text-blue-400 hover:text-blue-300 transition-colors"
              target="_blank"
              rel="noopener noreferrer"
            >
              QuickCV
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
};
