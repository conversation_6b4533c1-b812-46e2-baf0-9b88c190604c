import { useTranslations } from "next-intl";
import React from "react";
import { WebsiteFooter, WebsiteHero, WebsiteNav, WebsiteSection, WebsiteTemplateProps } from "@/components";
import { formatLocation, getFullName } from "@/components/features/resume/templates/shared/utils";

export const CreativeTemplate: React.FC<WebsiteTemplateProps> = ({ resume, website, className = "" }) => {
  const t = useTranslations("website.sections");
  const fullName = getFullName(resume.firstName || "", resume.lastName || "");
  const location = formatLocation(resume.city || "", resume.country || "");

  // Navigation sections
  const sections = [
    { id: "about", label: t("about") },
    { id: "experience", label: t("experience") },
    { id: "projects", label: t("projects") },
    { id: "skills", label: t("skills") },
    { id: "education", label: t("education") },
    { id: "contact", label: t("contact") },
  ];

  return (
    <div
      className={`creative-template min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 ${className}`}
    >
      {/* Navigation */}
      <WebsiteNav fullName={fullName} sections={sections} className="bg-white/90 backdrop-blur-sm border-0" />

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 start-10 w-20 h-20 bg-purple-400 rounded-full animate-pulse"></div>
          <div className="absolute top-32 end-20 w-16 h-16 bg-pink-400 rounded-full animate-bounce"></div>
          <div className="absolute bottom-20 start-1/4 w-12 h-12 bg-blue-400 rounded-full animate-pulse"></div>
          <div className="absolute bottom-32 end-1/3 w-24 h-24 bg-indigo-400 rounded-full animate-bounce"></div>
        </div>

        <WebsiteHero
          firstName={resume.firstName || ""}
          lastName={resume.lastName || ""}
          jobTitle={resume.jobTitle || ""}
          bio={resume.bio}
          photo={resume.photo}
          showPhoto={resume.showPhoto === 1}
          className="relative z-10 bg-gradient-to-r from-purple-600/10 to-pink-600/10 py-20"
        />
      </div>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-16 relative">
        {/* About Section */}
        <WebsiteSection title={t("about")} id="about" className="mb-20">
          <div className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/20">
            <div className="text-lg text-gray-700 leading-relaxed">
              {resume.bio ? (
                <div dangerouslySetInnerHTML={{ __html: resume.bio }} />
              ) : (
                <p>Creative professional specializing in {resume.jobTitle}.</p>
              )}
            </div>
            {location && (
              <div className="mt-6 flex items-center text-gray-600">
                <span className="me-2 text-purple-500">📍</span>
                <span>{location}</span>
              </div>
            )}
          </div>
        </WebsiteSection>

        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <WebsiteSection title={t("experience")} id="experience" className="mb-20">
            <div className="space-y-8">
              {resume.experiences.map((experience, index) => (
                <div
                  key={experience.id}
                  className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/20 relative overflow-hidden"
                >
                  {/* Creative accent */}
                  <div
                    className={`absolute top-0 start-0 w-2 h-full bg-gradient-to-b ${
                      index % 3 === 0
                        ? "from-purple-500 to-pink-500"
                        : index % 3 === 1
                          ? "from-blue-500 to-indigo-500"
                          : "from-pink-500 to-red-500"
                    }`}
                  ></div>

                  <div className="ms-6">
                    <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start mb-4">
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900 mb-2">{experience.title}</h3>
                        <p className="text-xl text-purple-600 font-semibold mb-2">{experience.company}</p>
                        {formatLocation(experience.city || "", experience.country || "") && (
                          <p className="text-gray-600">
                            {formatLocation(experience.city || "", experience.country || "")}
                          </p>
                        )}
                      </div>
                      <div className="text-gray-600 mt-4 lg:mt-0 lg:text-end">
                        <p className="font-medium px-4 py-2 bg-purple-100 rounded-full text-sm">
                          {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                        </p>
                      </div>
                    </div>
                    {experience.description && (
                      <div
                        dangerouslySetInnerHTML={{
                          __html: experience.description,
                        }}
                        className="text-gray-700 leading-relaxed"
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </WebsiteSection>
        )}

        {/* Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <WebsiteSection title={t("projects")} id="projects" className="mb-20">
            <div className="grid md:grid-cols-2 gap-8">
              {resume.projects.map((project, index) => (
                <div
                  key={project.id}
                  className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                >
                  {/* Creative project icon */}
                  <div
                    className={`w-12 h-12 rounded-xl mb-4 flex items-center justify-center text-white text-xl ${
                      index % 4 === 0
                        ? "bg-gradient-to-br from-purple-500 to-pink-500"
                        : index % 4 === 1
                          ? "bg-gradient-to-br from-blue-500 to-indigo-500"
                          : index % 4 === 2
                            ? "bg-gradient-to-br from-pink-500 to-red-500"
                            : "bg-gradient-to-br from-indigo-500 to-purple-500"
                    }`}
                  >
                    🚀
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-3">{project.title}</h3>
                  {project.client && <p className="text-purple-600 font-semibold mb-3">{project.client}</p>}
                  {project.description && (
                    <div
                      dangerouslySetInnerHTML={{ __html: project.description }}
                      className="text-gray-700 mb-4 leading-relaxed"
                    />
                  )}
                  {project.url && (
                    <a
                      href={project.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full hover:from-purple-600 hover:to-pink-600 transition-all duration-300 font-medium"
                    >
                      🔗 View Project
                    </a>
                  )}
                </div>
              ))}
            </div>
          </WebsiteSection>
        )}

        {/* Skills Section */}
        {resume.skills && resume.skills.length > 0 && (
          <WebsiteSection title={t("skills")} id="skills" className="mb-20">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Group skills by category */}
              {Object.entries(
                resume.skills.reduce(
                  (acc, skill) => {
                    const category = skill.category || "Other";
                    if (!acc[category]) acc[category] = [];
                    acc[category].push(skill);
                    return acc;
                  },
                  {} as Record<string, typeof resume.skills>,
                ),
              ).map(([category, categorySkills], index) => (
                <div
                  key={category}
                  className="bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20"
                >
                  <h3
                    className={`text-lg font-bold mb-4 ${
                      index % 3 === 0 ? "text-purple-600" : index % 3 === 1 ? "text-blue-600" : "text-pink-600"
                    }`}
                  >
                    {category}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {categorySkills.map((skill) => (
                      <span
                        key={skill.id}
                        className={`px-3 py-1 rounded-full text-sm font-medium text-white ${
                          index % 3 === 0
                            ? "bg-gradient-to-r from-purple-400 to-purple-600"
                            : index % 3 === 1
                              ? "bg-gradient-to-r from-blue-400 to-blue-600"
                              : "bg-gradient-to-r from-pink-400 to-pink-600"
                        }`}
                      >
                        {skill.name}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </WebsiteSection>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <WebsiteSection title={t("education")} id="education" className="mb-20">
            <div className="space-y-6">
              {resume.educations.map((education, _index) => (
                <div
                  key={education.id}
                  className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/20"
                >
                  <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{education.institution}</h3>
                      <p className="text-purple-600 font-semibold mb-2">
                        {education.degree}
                        {education.fieldOfStudy && ` in ${education.fieldOfStudy}`}
                      </p>
                      {formatLocation(education.city || "", education.country || "") && (
                        <p className="text-gray-600">{formatLocation(education.city || "", education.country || "")}</p>
                      )}
                    </div>
                    <div className="text-gray-600 mt-4 lg:mt-0">
                      <p className="font-medium px-4 py-2 bg-pink-100 rounded-full text-sm">
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </p>
                    </div>
                  </div>
                  {education.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: education.description,
                      }}
                      className="text-gray-700 mt-4 leading-relaxed"
                    />
                  )}
                </div>
              ))}
            </div>
          </WebsiteSection>
        )}

        {/* Contact Section */}
        <WebsiteSection title={t("contact")} id="contact">
          <div className="bg-white/70 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/20 text-center">
            <div className="mb-6">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Let's Connect!</h3>
              <p className="text-gray-600 mb-6">Ready to bring your next project to life?</p>
            </div>

            <div className="flex flex-wrap justify-center gap-6">
              {resume.email && (
                <a
                  href={`mailto:${resume.email}`}
                  className="flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full hover:from-purple-600 hover:to-pink-600 transition-all duration-300 font-medium"
                >
                  <span className="me-2">✉️</span>
                  {resume.email}
                </a>
              )}
              {resume.website && (
                <a
                  href={resume.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-full hover:from-blue-600 hover:to-indigo-600 transition-all duration-300 font-medium"
                >
                  <span className="me-2">🌐</span>
                  Website
                </a>
              )}
              {location && (
                <div className="flex items-center px-6 py-3 bg-gradient-to-r from-pink-500 to-red-500 text-white rounded-full font-medium">
                  <span className="me-2">📍</span>
                  {location}
                </div>
              )}
            </div>
          </div>
        </WebsiteSection>
      </main>

      {/* Footer */}
      <WebsiteFooter
        fullName={fullName}
        className="bg-gradient-to-r from-purple-600/10 to-pink-600/10 backdrop-blur-sm"
      />
    </div>
  );
};
