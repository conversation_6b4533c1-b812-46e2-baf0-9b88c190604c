import { useTranslations } from "next-intl";
import React from "react";
import { WebsiteNav, WebsiteTemplateProps } from "@/components";
import { formatLocation, getFullName } from "@/components/features/resume/templates/shared/utils";

export const MinimalTemplate: React.FC<WebsiteTemplateProps> = ({ resume, website, className = "" }) => {
  const t = useTranslations("website.sections");
  const fullName = getFullName(resume.firstName || "", resume.lastName || "");
  const location = formatLocation(resume.city || "", resume.country || "");

  // Navigation sections
  const sections = [
    { id: "about", label: t("about") },
    { id: "experience", label: t("experience") },
    { id: "projects", label: t("projects") },
    { id: "skills", label: t("skills") },
    { id: "education", label: t("education") },
    { id: "contact", label: t("contact") },
  ];

  return (
    <div className={`minimal-template min-h-screen bg-white ${className}`}>
      {/* Navigation */}
      <WebsiteNav fullName={fullName} sections={sections} className="bg-white border-b border-gray-100" />

      {/* Hero Section - Clean and Simple */}
      <section className="py-20 px-4 text-center border-b border-gray-100">
        {resume.showPhoto === 1 && resume.photo && (
          <div className="mb-8">
            <img
              alt={fullName}
              className="w-24 h-24 rounded-full object-cover mx-auto grayscale hover:grayscale-0 transition-all duration-500"
              src={resume.photo}
            />
          </div>
        )}
        <h1 className="text-4xl md:text-5xl font-light text-gray-900 mb-4 tracking-wide">{fullName}</h1>
        <p className="text-xl text-gray-600 mb-8 font-light">{resume.jobTitle}</p>
        {resume.bio && (
          <div
            dangerouslySetInnerHTML={{ __html: resume.bio }}
            className="text-gray-700 max-w-2xl mx-auto leading-relaxed font-light"
          />
        )}
      </section>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-16">
        {/* About Section */}
        <section className="mb-20" id="about">
          <h2 className="text-2xl font-light text-start text-gray-900 mb-8 pb-4 border-b border-gray-200">
            {t("about")}
          </h2>
          <div className="prose prose-lg max-w-none">
            {resume.bio ? (
              <div
                dangerouslySetInnerHTML={{ __html: resume.bio }}
                className="text-gray-700 leading-relaxed font-light"
              />
            ) : (
              <p className="text-gray-700 leading-relaxed font-light">
                Professional with expertise in {resume.jobTitle}.
              </p>
            )}
            {location && (
              <div className="mt-6 text-gray-600 font-light">
                <span className="me-2">📍</span>
                <span>{location}</span>
              </div>
            )}
          </div>
        </section>

        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <section className="mb-20" id="experience">
            <h2 className="text-2xl font-light text-gray-900 mb-8 pb-4 border-b border-gray-200">{t("experience")}</h2>
            <div className="space-y-12">
              {resume.experiences.map((experience) => (
                <div key={experience.id} className="relative">
                  <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-4">
                    <div>
                      <h3 className="text-xl font-medium text-gray-900 mb-2">{experience.title}</h3>
                      <p className="text-lg text-gray-600 mb-2 font-light">{experience.company}</p>
                      {formatLocation(experience.city || "", experience.country || "") && (
                        <p className="text-gray-500 font-light">
                          {formatLocation(experience.city || "", experience.country || "")}
                        </p>
                      )}
                    </div>
                    <div className="text-gray-500 mt-4 md:mt-0 md:text-end">
                      <p className="font-light text-sm">
                        {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                      </p>
                    </div>
                  </div>
                  {experience.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: experience.description,
                      }}
                      className="text-gray-700 leading-relaxed font-light ps-4 border-s-2 border-gray-100"
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <section className="mb-20" id="projects">
            <h2 className="text-2xl font-light text-gray-900 mb-8 pb-4 border-b border-gray-200">{t("projects")}</h2>
            <div className="grid gap-12">
              {resume.projects.map((project) => (
                <div key={project.id} className="border-b border-gray-100 pb-8 last:border-b-0">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-xl font-medium text-gray-900">{project.title}</h3>
                  </div>
                  {project.client && <p className="text-gray-600 font-light mb-3">{project.client}</p>}
                  {project.description && (
                    <div
                      dangerouslySetInnerHTML={{ __html: project.description }}
                      className="text-gray-700 mb-4 leading-relaxed font-light"
                    />
                  )}
                  {project.url && (
                    <a
                      href={project.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-gray-900 hover:text-gray-600 transition-colors font-light border-b border-gray-300 hover:border-gray-600"
                    >
                      View Project →
                    </a>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills Section */}
        {resume.skills && resume.skills.length > 0 && (
          <section className="mb-20" id="skills">
            <h2 className="text-2xl font-light text-gray-900 mb-8 pb-4 border-b border-gray-200">{t("skills")}</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Group skills by category */}
              {Object.entries(
                resume.skills.reduce(
                  (acc, skill) => {
                    const category = skill.category || "Skills";
                    if (!acc[category]) acc[category] = [];
                    acc[category].push(skill);
                    return acc;
                  },
                  {} as Record<string, typeof resume.skills>,
                ),
              ).map(([category, categorySkills]) => (
                <div key={category} className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">{category}</h3>
                  <div className="space-y-2">
                    {categorySkills.map((skill) => (
                      <div key={skill.id} className="text-gray-700 font-light">
                        {skill.name}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <section className="mb-20" id="education">
            <h2 className="text-2xl font-light text-gray-900 mb-8 pb-4 border-b border-gray-200">{t("education")}</h2>
            <div className="space-y-8">
              {resume.educations.map((education) => (
                <div key={education.id}>
                  <div className="flex flex-col md:flex-row md:justify-between md:items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">{education.institution}</h3>
                      <p className="text-gray-600 font-light mb-2">
                        {education.degree}
                        {education.fieldOfStudy && ` in ${education.fieldOfStudy}`}
                      </p>
                      {formatLocation(education.city || "", education.country || "") && (
                        <p className="text-gray-500 font-light">
                          {formatLocation(education.city || "", education.country || "")}
                        </p>
                      )}
                    </div>
                    <div className="text-gray-500 mt-4 md:mt-0">
                      <p className="font-light text-sm">
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </p>
                    </div>
                  </div>
                  {education.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: education.description,
                      }}
                      className="text-gray-700 mt-4 leading-relaxed font-light ps-4 border-s-2 border-gray-100"
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Contact Section */}
        <section id="contact">
          <h2 className="text-2xl font-light text-gray-900 mb-8 pb-4 border-b border-gray-200">{t("contact")}</h2>
          <div className="space-y-4">
            {resume.email && (
              <div className="flex items-center">
                <span className="text-gray-600 font-light w-20">Email</span>
                <a
                  href={`mailto:${resume.email}`}
                  className="text-gray-900 hover:text-gray-600 transition-colors font-light"
                >
                  {resume.email}
                </a>
              </div>
            )}
            {resume.website && (
              <div className="flex items-center">
                <span className="text-gray-600 font-light w-20">Website</span>
                <a
                  href={resume.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-900 hover:text-gray-600 transition-colors font-light"
                >
                  {resume.website}
                </a>
              </div>
            )}
            {location && (
              <div className="flex items-center">
                <span className="text-gray-600 font-light w-20">Location</span>
                <span className="text-gray-700 font-light">{location}</span>
              </div>
            )}
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t border-gray-100 py-8 text-center">
        <div className="max-w-4xl mx-auto px-4">
          <p className="text-gray-500 font-light mb-2">
            © {new Date().getFullYear()} {fullName}
          </p>
          <p className="text-gray-400 text-sm font-light">
            Created with{" "}
            <a
              href="https://quickcv.com"
              className="text-gray-600 hover:text-gray-900 transition-colors"
              target="_blank"
              rel="noopener noreferrer"
            >
              QuickCV
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
};
