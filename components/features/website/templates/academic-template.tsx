import { useTranslations } from "next-intl";
import React from "react";
import { WebsiteTemplateProps } from "@/components";
import { formatLocation, getFullName } from "@/components/features/resume/templates/shared/utils";

export const AcademicTemplate: React.FC<WebsiteTemplateProps> = ({ resume, website, className = "" }) => {
  const t = useTranslations("website.sections");
  const fullName = getFullName(resume.firstName || "", resume.lastName || "");
  const location = formatLocation(resume.city || "", resume.country || "");

  // Navigation sections
  const sections = [
    { id: "about", label: t("about") },
    { id: "education", label: t("education") },
    { id: "experience", label: t("experience") },
    { id: "research", label: "Research" },
    { id: "skills", label: t("skills") },
    { id: "contact", label: t("contact") },
  ];

  return (
    <div className={`academic-template min-h-screen bg-slate-50 ${className}`}>
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-50">
        <div className="max-w-5xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="text-xl font-serif text-slate-800">{fullName}</div>
            <div className="hidden md:flex space-x-8">
              {sections.map((section) => (
                <a
                  key={section.id}
                  href={`#${section.id}`}
                  className="text-slate-600 hover:text-slate-900 transition-colors font-medium text-sm"
                >
                  {section.label}
                </a>
              ))}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-16 px-4 bg-white border-b border-slate-200">
        <div className="max-w-5xl mx-auto">
          <div className="text-center">
            {resume.showPhoto === 1 && resume.photo && (
              <div className="mb-8">
                <img
                  alt={fullName}
                  className="w-32 h-32 rounded-full object-cover mx-auto border-4 border-slate-200"
                  src={resume.photo}
                />
              </div>
            )}

            <h1 className="text-4xl md:text-5xl font-serif text-slate-900 mb-4">{fullName}</h1>

            <p className="text-xl text-slate-600 mb-6 font-medium">{resume.jobTitle}</p>

            {resume.bio && (
              <div
                dangerouslySetInnerHTML={{ __html: resume.bio }}
                className="text-slate-700 max-w-3xl mx-auto leading-relaxed text-lg"
              />
            )}

            {location && (
              <div className="mt-6 text-slate-600">
                <span className="me-2">📍</span>
                <span>{location}</span>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="max-w-5xl mx-auto px-4 py-12">
        {/* About Section */}
        <section className="mb-16" id="about">
          <h2 className="text-3xl font-serif text-slate-900 mb-8 pb-4 border-b-2 border-slate-200">{t("about")}</h2>
          <div className="bg-white p-8 rounded-lg shadow-sm border border-slate-200">
            <div className="prose prose-lg prose-slate max-w-none">
              {resume.bio ? (
                <div dangerouslySetInnerHTML={{ __html: resume.bio }} />
              ) : (
                <p>Academic and researcher with expertise in {resume.jobTitle}.</p>
              )}
            </div>
          </div>
        </section>

        {/* Education Section - Priority for academic template */}
        {resume.educations && resume.educations.length > 0 && (
          <section className="mb-16" id="education">
            <h2 className="text-3xl font-serif text-slate-900 mb-8 pb-4 border-b-2 border-slate-200">
              {t("education")}
            </h2>
            <div className="space-y-8">
              {resume.educations.map((education) => (
                <div key={education.id} className="bg-white p-8 rounded-lg shadow-sm border border-slate-200">
                  <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start mb-4">
                    <div className="flex-1">
                      <h3 className="text-2xl font-serif text-slate-900 mb-3">{education.institution}</h3>
                      <div className="mb-2">
                        <p className="text-lg text-slate-700 font-medium">{education.degree}</p>
                        {education.fieldOfStudy && <p className="text-slate-600 italic">{education.fieldOfStudy}</p>}
                      </div>
                      {formatLocation(education.city || "", education.country || "") && (
                        <p className="text-slate-600">
                          {formatLocation(education.city || "", education.country || "")}
                        </p>
                      )}
                    </div>
                    <div className="text-slate-600 mt-4 lg:mt-0 lg:text-end">
                      <p className="font-medium bg-slate-100 px-4 py-2 rounded">
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </p>
                    </div>
                  </div>
                  {education.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: education.description,
                      }}
                      className="text-slate-700 leading-relaxed border-s-4 border-slate-200 ps-6"
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <section className="mb-16" id="experience">
            <h2 className="text-3xl font-serif text-slate-900 mb-8 pb-4 border-b-2 border-slate-200">
              {t("experience")}
            </h2>
            <div className="space-y-8">
              {resume.experiences.map((experience) => (
                <div key={experience.id} className="bg-white p-8 rounded-lg shadow-sm border border-slate-200">
                  <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start mb-4">
                    <div>
                      <h3 className="text-xl font-serif text-slate-900 mb-2">{experience.title}</h3>
                      <p className="text-lg text-slate-700 font-medium mb-2">{experience.company}</p>
                      {formatLocation(experience.city || "", experience.country || "") && (
                        <p className="text-slate-600">
                          {formatLocation(experience.city || "", experience.country || "")}
                        </p>
                      )}
                    </div>
                    <div className="text-slate-600 mt-4 lg:mt-0 lg:text-end">
                      <p className="font-medium bg-slate-100 px-4 py-2 rounded">
                        {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                      </p>
                    </div>
                  </div>
                  {experience.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: experience.description,
                      }}
                      className="text-slate-700 leading-relaxed border-s-4 border-slate-200 ps-6"
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Research/Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <section className="mb-16" id="research">
            <h2 className="text-3xl font-serif text-slate-900 mb-8 pb-4 border-b-2 border-slate-200">
              Research & Publications
            </h2>
            <div className="space-y-8">
              {resume.projects.map((project, index) => (
                <div key={project.id} className="bg-white p-8 rounded-lg shadow-sm border border-slate-200">
                  <div className="flex items-start mb-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-slate-800 text-white rounded-full flex items-center justify-center font-serif font-bold text-sm me-4 mt-1">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-serif text-slate-900 mb-3">{project.title}</h3>
                      {project.client && <p className="text-slate-600 font-medium mb-3 italic">{project.client}</p>}
                      {project.description && (
                        <div
                          dangerouslySetInnerHTML={{
                            __html: project.description,
                          }}
                          className="text-slate-700 mb-4 leading-relaxed"
                        />
                      )}
                      {project.url && (
                        <a
                          href={project.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-slate-800 hover:text-slate-600 transition-colors font-medium border-b border-slate-300 hover:border-slate-500"
                        >
                          📄 View Publication
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills Section */}
        {resume.skills && resume.skills.length > 0 && (
          <section className="mb-16" id="skills">
            <h2 className="text-3xl font-serif text-slate-900 mb-8 pb-4 border-b-2 border-slate-200">{t("skills")}</h2>
            <div className="bg-white p-8 rounded-lg shadow-sm border border-slate-200">
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {/* Group skills by category */}
                {Object.entries(
                  resume.skills.reduce(
                    (acc, skill) => {
                      const category = skill.category || "Other";
                      if (!acc[category]) acc[category] = [];
                      acc[category].push(skill);
                      return acc;
                    },
                    {} as Record<string, typeof resume.skills>,
                  ),
                ).map(([category, categorySkills]) => (
                  <div key={category}>
                    <h3 className="text-lg font-serif text-slate-900 mb-4 pb-2 border-b border-slate-200">
                      {category}
                    </h3>
                    <ul className="space-y-2">
                      {categorySkills.map((skill) => (
                        <li key={skill.id} className="text-slate-700 flex items-center">
                          <span className="w-2 h-2 bg-slate-400 rounded-full me-3 flex-shrink-0"></span>
                          {skill.name}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Contact Section */}
        <section id="contact">
          <h2 className="text-3xl font-serif text-slate-900 mb-8 pb-4 border-b-2 border-slate-200">{t("contact")}</h2>
          <div className="bg-white p-8 rounded-lg shadow-sm border border-slate-200">
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-serif text-slate-900 mb-4">Get in Touch</h3>
                <div className="space-y-4">
                  {resume.email && (
                    <div className="flex items-center">
                      <span className="w-20 text-slate-600 font-medium">Email</span>
                      <a
                        href={`mailto:${resume.email}`}
                        className="text-slate-800 hover:text-slate-600 transition-colors"
                      >
                        {resume.email}
                      </a>
                    </div>
                  )}
                  {resume.website && (
                    <div className="flex items-center">
                      <span className="w-20 text-slate-600 font-medium">Website</span>
                      <a
                        href={resume.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-slate-800 hover:text-slate-600 transition-colors"
                      >
                        {resume.website}
                      </a>
                    </div>
                  )}
                  {location && (
                    <div className="flex items-center">
                      <span className="w-20 text-slate-600 font-medium">Location</span>
                      <span className="text-slate-700">{location}</span>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-serif text-slate-900 mb-4">Academic Profile</h3>
                <p className="text-slate-700 leading-relaxed">
                  {resume.jobTitle} with focus on academic excellence and research. Available for collaborations,
                  consultations, and academic opportunities.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-slate-200 py-8 text-center">
        <div className="max-w-5xl mx-auto px-4">
          <p className="text-slate-600 mb-2 font-serif">
            © {new Date().getFullYear()} {fullName}
          </p>
          <p className="text-slate-500 text-sm">
            Academic profile created with{" "}
            <a
              href="https://quickcv.com"
              className="text-slate-700 hover:text-slate-900 transition-colors"
              target="_blank"
              rel="noopener noreferrer"
            >
              QuickCV
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
};
