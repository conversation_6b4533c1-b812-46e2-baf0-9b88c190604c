import { useLocale } from "next-intl";

// RTL utility hook
export const useRTL = () => {
  const locale = useLocale();
  return locale === "ar";
};

// RTL-aware flex utilities
export const getRTLFlexClasses = (isRTL: boolean) => ({
  flexRow: isRTL ? "flex-row-reverse" : "flex-row",
  justifyStart: "justify-start", // This works automatically with RTL
  textStart: "text-start", // This works automatically with RTL
  textEnd: "text-end", // This works automatically with RTL
  spaceX: isRTL ? "space-x-reverse space-x-6" : "space-x-6",
  marginStart: "ms-3",
  marginEnd: "me-3",
});
