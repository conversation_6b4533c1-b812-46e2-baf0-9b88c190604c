"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ody,
  <PERSON><PERSON><PERSON><PERSON>,
  Chip,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  <PERSON>,
  Tooltip,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { UpgradeModal } from "@/components/payment";
import EmptyState from "@/components/shared/empty-state";
import { FullWebsite } from "@/db/schema";
import { type SortOption, useFilterSort } from "@/hooks/use-filter-sort";
import WebsitePageHeader from "./website-page-header";

interface WebsitesPageProps {
  websites: FullWebsite[];
}

export function WebsitesPage({ websites }: WebsitesPageProps) {
  const t = useTranslations("websites");
  const tCommon = useTranslations("common");
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<number | null>(null);
  const { isOpen: isUpgradeOpen, onOpen: onUpgradeOpen, onClose: onUpgradeClose } = useDisclosure();

  const utils = trpc.useUtils();
  const togglePublicMutation = trpc.websites.toggleWebsitePublic.useMutation();
  const deleteWebsiteMutation = trpc.websites.deleteWebsite.useMutation();

  // Use the custom hook for filtering and sorting
  const {
    filter,
    setFilter,
    sort,
    setSort,
    filteredAndSortedItems: filteredAndSortedWebsites,
    resetFilters,
  } = useFilterSort(websites, {
    filterFn: (website, filterValue): boolean => {
      switch (filterValue) {
        case "published":
          return !!website.isPublic;
        case "unpublished":
          return !website.isPublic;
        case "recent": {
          const createdDate = new Date(website.createdAt);
          const daysSinceCreation = (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24);
          return daysSinceCreation <= 7;
        }
        default:
          return true;
      }
    },
    getItemName: (website) => website.resume?.title || "",
  });

  const handleTogglePublic = async (websiteId: number) => {
    setIsLoading(websiteId);
    try {
      const result = await togglePublicMutation.mutateAsync({ websiteId });
      if (result.success) {
        // Get the current website to determine the action
        const website = websites.find((w) => w.id === websiteId);
        toast.success(website?.isPublic ? "Website unpublished successfully" : "Website published successfully");
      }
      await utils.websites.getUserWebsites.invalidate();
      router.refresh();
    } catch (error: any) {
      console.error("Error toggling website public status:", error);
      // Check if it's a FORBIDDEN error (premium required)
      if (error?.data?.code === "FORBIDDEN") {
        onUpgradeOpen();
      } else {
        toast.error("Failed to update website status");
      }
    } finally {
      setIsLoading(null);
    }
  };

  const handleDeleteWebsite = async (websiteId: number) => {
    if (!confirm(t("confirm_delete"))) return;

    setIsLoading(websiteId);
    try {
      const result = await deleteWebsiteMutation.mutateAsync({ websiteId });
      if (result.success) {
        toast.success("Website deleted successfully");
      }
      await utils.websites.getUserWebsites.invalidate();
      router.refresh();
    } catch (error) {
      console.error("Error deleting website:", error);
      toast.error("Failed to delete website");
    } finally {
      setIsLoading(null);
    }
  };

  const getWebsiteUrl = (slug: string) => {
    return `${window.location.origin}/cv/${slug}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("URL copied to clipboard!");
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.charAt(0) || "";
    const last = lastName?.charAt(0) || "";
    return (first + last).toUpperCase() || "W";
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <WebsitePageHeader
        websiteCount={websites?.length || 0}
        onFilterChange={setFilter}
        onSortChange={setSort}
        onExportAll={() => toast.success("Export feature coming soon!")}
        onReset={resetFilters}
        currentFilter={filter}
        currentSort={sort}
        filteredCount={filteredAndSortedWebsites.length}
        showCreateButton={true}
      />

      {websites?.length === 0 ? (
        <EmptyState
          icon="tabler:world-plus"
          title={t("no_websites_title")}
          description={t("no_websites_subtitle")}
          variant="gradient"
          size="lg"
          actionButton={
            <Button
              as={Link}
              href="/websites/builder"
              color="primary"
              size="lg"
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-200"
              startContent={<Icon icon="tabler:rocket" className="w-5 h-5" />}
            >
              {t("create_first_website")}
            </Button>
          }
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedWebsites?.map((website) => (
            <Card
              key={website.id}
              className="group relative overflow-hidden hover:shadow-2xl transition-all duration-300 border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-md"
            >
              <CardHeader className="pb-3">
                <div className="flex items-center gap-4 w-full">
                  <Avatar
                    name={getInitials(website.resume?.firstName, website.resume?.lastName)}
                    className="bg-gradient-to-br from-blue-500 to-purple-600 text-white"
                    size="lg"
                  />
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100 truncate">
                      {`${website.resume?.firstName || ""} ${website.resume?.lastName || ""}`.trim() || "Untitled"}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {website.resume?.jobTitle || "No job title"}
                    </p>
                  </div>
                  <Chip
                    size="sm"
                    variant="flat"
                    className={
                      website.isPublic
                        ? "bg-gradient-to-r from-green-400 to-emerald-500 text-white"
                        : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                    }
                    startContent={<Icon icon={website.isPublic ? "tabler:world" : "tabler:lock"} className="w-3 h-3" />}
                  >
                    {website.isPublic ? t("published") : t("draft")}
                  </Chip>
                </div>
              </CardHeader>
              <CardBody className="pt-0">
                {/* Template Info */}
                <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <Icon icon="tabler:template" className="w-4 h-4 text-gray-500" />
                    <span className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t("template")}
                    </span>
                  </div>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {website.websiteTemplate?.name || "Unknown Template"}
                  </p>
                </div>

                {/* Website URL */}
                {website.isPublic && (
                  <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex items-center justify-between gap-2 mb-2">
                      <span className="text-xs text-blue-600 dark:text-blue-400 uppercase tracking-wider flex items-center gap-1">
                        <Icon icon="tabler:link" className="w-4 h-4" />
                        {t("website_url")}
                      </span>
                      <Tooltip content="Copy URL">
                        <Button
                          size="sm"
                          isIconOnly
                          variant="light"
                          className="min-w-unit-6 w-unit-6 h-unit-6"
                          onPress={() => copyToClipboard(getWebsiteUrl(website.slug))}
                        >
                          <Icon icon="tabler:copy" className="w-3 h-3" />
                        </Button>
                      </Tooltip>
                    </div>
                    <Link
                      href={getWebsiteUrl(website.slug)}
                      target="_blank"
                      className="text-sm text-blue-600 dark:text-blue-400 hover:underline break-all block"
                    >
                      {getWebsiteUrl(website.slug)}
                    </Link>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2 mt-4">
                  <Button
                    as={Link}
                    href={`/websites/${website.id}/builder`}
                    size="sm"
                    variant="flat"
                    className="flex-1 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30 text-blue-700 dark:text-blue-300"
                    startContent={<Icon icon="tabler:pencil" className="w-4 h-4" />}
                  >
                    {tCommon("edit")}
                  </Button>

                  <Dropdown>
                    <DropdownTrigger>
                      <Button
                        size="sm"
                        isIconOnly
                        variant="flat"
                        className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600"
                      >
                        <Icon icon="tabler:dots-vertical" className="w-4 h-4" />
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu aria-label="Website actions">
                      <DropdownItem
                        key="view"
                        startContent={<Icon icon="tabler:external-link" className="w-4 h-4" />}
                        as={Link}
                        href={getWebsiteUrl(website.slug)}
                        target="_blank"
                        className={!website.isPublic ? "opacity-50 pointer-events-none" : ""}
                      >
                        View Website
                      </DropdownItem>
                      <DropdownItem
                        key="toggle"
                        startContent={
                          <Icon icon={website.isPublic ? "tabler:eye-off" : "tabler:eye"} className="w-4 h-4" />
                        }
                        onPress={() => handleTogglePublic(website.id)}
                        className={isLoading === website.id ? "opacity-50" : ""}
                      >
                        {website.isPublic ? t("unpublish") : t("publish")}
                      </DropdownItem>
                      <DropdownItem
                        key="analytics"
                        startContent={<Icon icon="tabler:chart-bar" className="w-4 h-4" />}
                        className="opacity-50 pointer-events-none"
                      >
                        Analytics (Coming Soon)
                      </DropdownItem>
                      <DropdownItem
                        key="delete"
                        className="text-danger"
                        color="danger"
                        startContent={<Icon icon="tabler:trash" className="w-4 h-4" />}
                        onPress={() => handleDeleteWebsite(website.id)}
                      >
                        {tCommon("delete")}
                      </DropdownItem>
                    </DropdownMenu>
                  </Dropdown>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      )}

      {/* Upgrade Modal */}
      <UpgradeModal isOpen={isUpgradeOpen} onClose={onUpgradeClose} feature="website" />
    </div>
  );
}
