"use client";

import { useTranslations } from "next-intl";
import { trpc } from "@/app/_trpc/client";
import { WebsiteBuilder } from "@/components/features/website/WebsiteBuilder";
import LoaderComponent from "@/components/shared/common/LoaderComponent";
import SpinnerComponent from "@/components/shared/common/spinner";
import { Website } from "@/db/schema";

export function WebsiteBuilderPage({ website }: { website: Website }) {
  const t = useTranslations("website.builder");

  const getResumes = trpc.resumes.getResumes.useQuery();

  const { isLoading: isLoadingResumes, data: resumes = [] } = getResumes;

  // Get website templates using TRPC if not provided via SSR
  const getTemplates = trpc.websites.getWebsiteTemplates.useQuery();

  const { isLoading: isLoadingTemplates, data: templates = [] } = getTemplates;

  // Show loading spinner while fetching data (only if no SSR data provided)
  if (isLoadingResumes || isLoadingTemplates) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto py-8 px-4">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{t("title")}</h1>
            <p className="text-gray-600">{t("description")}</p>
          </div>
          <div className="flex justify-center">
            <LoaderComponent />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-8 px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{website.resumeId ? t("title") : t("create_title")}</h1>
          <p className="text-gray-600">{website.resumeId ? t("description") : t("create_description")}</p>
        </div>

        <WebsiteBuilder resumes={resumes} website={website} templates={templates} />
      </div>
    </div>
  );
}
