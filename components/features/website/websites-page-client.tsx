"use client";

import { trpc } from "@/app/_trpc/client";
import { WebsitesPage } from "@/components";
import LoaderComponent from "@/components/shared/common/LoaderComponent";
import SpinnerComponent from "../../shared/common/spinner";

export function WebsitesPageClient() {
  const { data: websites, isLoading, error } = trpc.websites.getUserWebsites.useQuery();

  if (isLoading) {
    return <LoaderComponent />;
  }

  if (error) {
    return (
      <div className="text-center py-16">
        <p className="text-red-600">Error loading websites: {error.message}</p>
      </div>
    );
  }

  return <WebsitesPage websites={websites || []} />;
}
