"use client";

import { But<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { useTRPCLoading } from "@/lib/use-loading-action";

interface LinkedInCreateResumeButtonProps {
  onSuccess?: (resumeId: number) => void;
}

export function LinkedInCreateResumeButton({ onSuccess }: LinkedInCreateResumeButtonProps) {
  const t = useTranslations("resumes");
  const [isConnecting, setIsConnecting] = useState(false);

  const importMutation = trpc.resumes.importFromLinkedIn.useMutation();
  const { wrapTRPCMutation } = useTRPCLoading();

  const wrappedImport = wrapTRPCMutation(importMutation, "linkedin-import", t("importing_from_linkedin"));

  const handleLinkedInAuth = () => {
    setIsConnecting(true);
    // Open LinkedIn OAuth in a popup window
    const popup = window.open(
      "/api/linkedin/auth",
      "linkedin-auth",
      "width=600,height=700,scrollbars=yes,resizable=yes",
    );

    // Listen for the popup to close or get redirected
    const checkClosed = setInterval(() => {
      if (popup?.closed) {
        clearInterval(checkClosed);
        setIsConnecting(false);
        // Check if user is now connected by trying to import
        handleImport();
      }
    }, 1000);

    // Cleanup if popup is still open after 5 minutes
    setTimeout(() => {
      if (popup && !popup.closed) {
        popup.close();
        clearInterval(checkClosed);
        setIsConnecting(false);
        toast.error(t("linkedin_auth_timeout"));
      }
    }, 300000);
  };

  const handleImport = async () => {
    try {
      // Call import to create a new resume
      const result = await wrappedImport.mutateAsync();

      if (result.success) {
        const { importedSections, resumeId } = result;
        let message = t("resume_created_from_linkedin");

        if (importedSections.personalInfo) {
          message += ` ${t("personal_info_imported")}`;
        }
        if (importedSections.experiences > 0) {
          message += ` ${importedSections.experiences} ${t("experiences_imported")}`;
        }
        if (importedSections.educations > 0) {
          message += ` ${importedSections.educations} ${t("educations_imported")}`;
        }
        if (importedSections.skills > 0) {
          message += ` ${importedSections.skills} ${t("skills_imported")}`;
        }

        toast.success(message);
        onSuccess?.(resumeId);
      }
    } catch (error: any) {
      if (error.message?.includes("not connected")) {
        // User needs to connect LinkedIn first
        handleLinkedInAuth();
      } else {
        console.error("LinkedIn import error:", error);
        toast.error(t("linkedin_import_failed"));
      }
    }
  };

  const isLoading = isConnecting || importMutation.isPending;

  return (
    <div className="flex flex-col gap-4 p-4">
      <p className="text-center text-default-600">{t("linkedin_create_description")}</p>
      <p className="text-sm text-default-500">{t("linkedin_create_info")}</p>

      <Button
        color="primary"
        size="lg"
        className="w-full"
        startContent={<Icon icon="simple-icons:linkedin" width={16} height={16} />}
        onPress={handleImport}
        isLoading={isLoading}
        isDisabled={isLoading}
      >
        {isConnecting ? t("connecting_linkedin") : t("connect_linkedin")}
      </Button>
    </div>
  );
}
