"use client";
import { Button } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";

interface ResumePreviewControlsProps {
  handleFullScreen: () => void;
  exportPdf: () => void;
  exportError?: string | null;
  isGeneratingPDF?: boolean;
}

export const ResumePreviewControls: React.FC<ResumePreviewControlsProps> = ({
  exportPdf,
  handleFullScreen,
  exportError = null,
  isGeneratingPDF = false,
}) => {
  const t = useTranslations();
  return (
    <div className="preview-controls flex items-center justify-end p-3  border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
      <div className="flex items-center gap-2">
        {/* Zoom Controls */}
        <div className="flex items-center gap-1 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700">
          <Button isIconOnly size="sm" variant="light" onPress={handleFullScreen}>
            <Icon height={16} icon="lucide:fullscreen" width={16} />
          </Button>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2">
          <div className="flex gap-2">
            <Button
              color={exportError ? "danger" : "primary"}
              size="sm"
              startContent={
                isGeneratingPDF ? (
                  <Icon icon="lucide:loader-2" className="animate-spin" />
                ) : (
                  <Icon icon="lucide:download" />
                )
              }
              onPress={exportPdf}
              isLoading={isGeneratingPDF}
              isDisabled={isGeneratingPDF}
            >
              {isGeneratingPDF ? "Generating PDF..." : t("preview.download_pdf")}
            </Button>
          </div>

          {/* Error Message */}
          {exportError && (
            <div className="text-sm text-danger flex items-center gap-1">
              <Icon height={16} icon="lucide:alert-circle" width={16} />
              <span>{exportError}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
