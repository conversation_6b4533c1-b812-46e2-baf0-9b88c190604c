"use client";

import { Input } from "@heroui/react";
import { useTranslations } from "next-intl";
import React from "react";
import { UserPhotoUploadThing } from "@/components";
import LoaderComponent from "@/components/shared/common/LoaderComponent";
import { FullResume } from "@/db/schema";
import { useFormStore } from "@/lib/form-store";
import { CheckboxField, DatePickerComponent, TrixEditorField } from "../../forms";
import SpinnerComponent from "../../shared/common/spinner";

const PersonalFormFields: React.FC<{ data?: FullResume }> = ({ data }) => {
  const t = useTranslations("forms");

  const { handleInputChange, updateField, formData } = useFormStore();

  if (!data) return <LoaderComponent />;

  // Use formData if available, otherwise fallback to initial data
  const currentData = formData || data;

  return (
    <div className="grid grid-cols-2 gap-4 h-full w-full">
      <h2 className="col-span-full text-2xl font-bold text-gray-800 dark:text-gray-100">{t("personal_information")}</h2>

      <div className="col-span-full grid grid-cols-8 gap-2">
        <div className="col-span-4 md:col-span-3 my-2 flex flex-col gap-4 px-2 justify-center items-start w-full">
          <UserPhotoUploadThing
            photo={currentData.photo}
            resumeId={currentData.id}
            onPhotoUpdate={(photoUrl) => updateField("photo", photoUrl)}
          />
          <CheckboxField
            className={"w-full"}
            defaultSelected={currentData.showPhoto === 1}
            name="showPhoto"
            onChange={(value) => updateField("showPhoto", value)}
          >
            {t("showPhoto")}
          </CheckboxField>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 col-span-4 md:col-span-5">
          <Input
            label={t("firstName")}
            name="firstName"
            value={currentData?.firstName ?? ""}
            variant="bordered"
            onChange={handleInputChange}
          />

          <Input
            label={t("lastName")}
            name="lastName"
            value={currentData?.lastName ?? ""}
            variant="bordered"
            onChange={handleInputChange}
          />

          <Input
            label={t("jobTitle")}
            name="jobTitle"
            value={currentData?.jobTitle ?? ""}
            variant="bordered"
            onChange={handleInputChange}
          />

          <Input
            label={t("email")}
            name="email"
            type="email"
            value={currentData?.email ?? ""}
            variant="bordered"
            onChange={handleInputChange}
          />

          <DatePickerComponent
            defaultValue={currentData?.birthDate ?? ""}
            label={t("birthDate")}
            name="birthDate"
            onChange={(value) => updateField("birthDate", value)}
          />

          <Input
            label={t("website")}
            name="website"
            value={currentData?.website ?? ""}
            variant="bordered"
            onChange={handleInputChange}
          />
        </div>
      </div>

      <Input
        label={t("city")}
        name="city"
        value={currentData?.city ?? ""}
        variant="bordered"
        onChange={handleInputChange}
      />

      <Input
        label={t("street")}
        name="street"
        value={currentData?.street ?? ""}
        variant="bordered"
        onChange={handleInputChange}
      />

      <Input
        label={t("country")}
        name="country"
        value={currentData?.country ?? ""}
        variant="bordered"
        onChange={handleInputChange}
      />

      <Input
        label={t("address")}
        name="address"
        value={currentData?.address ?? ""}
        variant="bordered"
        onChange={handleInputChange}
      />

      <TrixEditorField
        className="col-span-full"
        id="bio"
        label={t("bio")}
        name="bio"
        value={currentData?.bio ?? ""}
        onChange={(value) => updateField("bio", value)}
        enableAI={true}
        resumeContext={currentData}
        itemContext={currentData}
        schemaEntity="bio"
      />
    </div>
  );
};

export default PersonalFormFields;
