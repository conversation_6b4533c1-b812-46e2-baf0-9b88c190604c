"use client";

import { useTranslations } from "next-intl";
import EmptyState from "@/components/shared/empty-state";
import CreateResumeButton from "./createResumeButton";

export default function ResumeEmptyState() {
  const t = useTranslations("resumes");
  return (
    <EmptyState
      icon="lucide:file-text"
      title={t("no_resumes_title")}
      description={t("no_resumes_description")}
      actionButton={<CreateResumeButton className="px-8" size="lg" />}
      showIconBadge={true}
      iconBadge="lucide:plus"
    />
  );
}
