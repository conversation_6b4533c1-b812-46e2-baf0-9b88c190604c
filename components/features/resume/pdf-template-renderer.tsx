"use client";

import React from "react";

// Direct template imports for client-side rendering
import {AzurillTemplate} from "@/components/features/resume/templates";
import BronzorTemplate from "@/components/features/resume/templates/bronzor-template";
import { ChikoritaTemplate } from "@/components/features/resume/templates/chikorita-template";
import DittoTemplate from "@/components/features/resume/templates/ditto-template";
import GengarTemplate from "@/components/features/resume/templates/gengar-template";
import GlalieTemplate from "@/components/features/resume/templates/glalie-template";
import KakunaTemplate from "@/components/features/resume/templates/kakuna-template";
import LeafishTemplate from "@/components/features/resume/templates/leafish-template";
import NosepassTemplate from "@/components/features/resume/templates/nosepass-template";
import OnyxTemplate from "@/components/features/resume/templates/onyx-template";
import PikachuTemplate from "@/components/features/resume/templates/pikachu-template";
import RhyhornTemplate from "@/components/features/resume/templates/rhyhorn-template";

interface PDFTemplateRendererProps {
  resume: any;
  className?: string;
}

export function PDFTemplateRenderer({ resume, className }: PDFTemplateRendererProps) {
  // Client-side template mapping
  const templateComponents = {
    azurill: AzurillTemplate,
    bronzor: BronzorTemplate,
    chikorita: ChikoritaTemplate,
    ditto: DittoTemplate,
    gengar: GengarTemplate,
    glalie: GlalieTemplate,
    kakuna: KakunaTemplate,
    leafish: LeafishTemplate,
    nosepass: NosepassTemplate,
    onyx: OnyxTemplate,
    pikachu: PikachuTemplate,
    rhyhorn: RhyhornTemplate,
  };

  // Get template slug from resume templateId
  const templateSlug = resume.template?.slug || "bronzor";
  const TemplateComponent = templateComponents[templateSlug as keyof typeof templateComponents] || BronzorTemplate;

  return <TemplateComponent className={className} resume={resume} />;
}
