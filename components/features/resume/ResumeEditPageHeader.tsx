"use client";

import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";

export const ResumeEditPageHeader = () => {
  const t = useTranslations();

  return (
    <div className="flex-shrink-0 border-b border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
      <div className="px-6 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <Icon icon="lucide:edit" className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">{t("resume.editor.title")}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("resume.editor.description")}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="px-3 py-1 rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm font-medium">
            {t("resume.editor.auto_saved")}
          </div>
        </div>
      </div>
    </div>
  );
};
