"use client";

import { <PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useRef, useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { useTRPCLoading } from "@/lib/use-loading-action";

interface PDFCreateResumeButtonProps {
  onSuccess?: (resumeId: number) => void;
}

export function PDFCreateResumeButton({ onSuccess }: PDFCreateResumeButtonProps) {
  const t = useTranslations("resumes");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const importMutation = trpc.resumes.importFromPDF.useMutation();
  const { wrapTRPCMutation } = useTRPCLoading();

  const wrappedImport = wrapTRPCMutation(importMutation, "pdf-import", t("importing_from_pdf"));

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset input
    event.target.value = "";

    // Validate file type
    if (file.type !== "application/pdf") {
      toast.error(t("invalid_file_type"));
      return;
    }

    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error(t("file_too_large"));
      return;
    }

    setIsProcessing(true);

    try {
      // Parse PDF using API endpoint
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/resume/parse-pdf", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to parse PDF");
      }

      const { data: pdfData, summary } = await response.json();

      // Import parsed data to create new resume
      const result = await wrappedImport.mutateAsync({
        pdfData,
      });

      if (result.success) {
        const { importedSections, resumeId } = result;
        let message = t("resume_created_from_pdf");

        if (importedSections.personalInfo) {
          message += ` ${t("personal_info_imported")}`;
        }
        if (importedSections.experiences > 0) {
          message += ` ${importedSections.experiences} ${t("experiences_imported")}`;
        }
        if (importedSections.educations > 0) {
          message += ` ${importedSections.educations} ${t("educations_imported")}`;
        }
        if (importedSections.skills > 0) {
          message += ` ${importedSections.skills} ${t("skills_imported")}`;
        }

        toast.success(message);
        onSuccess?.(resumeId);
      }
    } catch (error: any) {
      console.error("PDF import error:", error);
      toast.error(error.message || t("pdf_import_failed"));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const isLoading = isProcessing || importMutation.isPending;

  return (
    <div className="flex flex-col gap-4 p-4">
      <p className="text-center text-default-600">{t("pdf_create_description")}</p>
      <p className="text-sm text-default-500">{t("pdf_create_info")}</p>

      <Button
        color="secondary"
        size="lg"
        className="w-full"
        startContent={<Icon icon="tabler:file-type-pdf" width={16} height={16} />}
        onPress={handleButtonClick}
        isLoading={isLoading}
        isDisabled={isLoading}
      >
        {isProcessing ? t("processing_pdf") : t("upload_pdf_file")}
      </Button>

      <input ref={fileInputRef} type="file" accept=".pdf" onChange={handleFileSelect} style={{ display: "none" }} />
    </div>
  );
}
