import Image from "next/image";
import React from "react";
import {
  CertificationItem,
  EducationItem,
  ExperienceItem,
  formatLocation,
  getFullName,
  ProfessionalSummary,
  ProjectItem,
  SocialProfile,
  TemplateProps,
  useSectionTranslations,
  useTemplateLocale,
} from "./shared";

// Custom section component with red header for Onyx template
const OnyxSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="resume-section mb-6">
    <h2 className="section-title text-lg font-bold mb-3 text-red-600 border-b border-gray-300 pb-1">{title}</h2>
    {children}
  </section>
);

// Custom contact component with red icons for Onyx template
const OnyxContact: React.FC<{
  icon: string;
  children: React.ReactNode;
}> = ({ icon, children }) => (
  <div className="flex items-center">
    <span className="text-red-600 me-2">{icon}</span>
    {children}
  </div>
);

/**
 * Onyx Resume Template - Clean Professional Layout
 * - Photo and contact info header with social media links
 * - Single-column layout with red section headers
 * - Clean typography and minimal styling
 * - Professional appearance matching reference design
 * - ATS-friendly structure with proper hierarchy
 */
export const OnyxTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = useSectionTranslations();
  const fullName = getFullName(resume.firstName, resume.lastName, locale);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div className={`onyx-template bg-white text-gray-900 font-sans ${className}`}>
      {/* Page wrapper with A4 proportions */}
      <div className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-white p-8">
        {/* Header with Photo, Name, Title, Contact Info and Social Links */}
        <header className="resume-header mb-8 pb-6 border-b border-gray-300">
          <div className="flex items-start justify-between">
            {/* Left side: Photo, Name, Title, Contact */}
            <div className="flex items-start gap-4 flex-1">
              {/* Photo */}
              {resume.showPhoto && resume.photo ? (
                <div className="flex-shrink-0">
                  <Image
                    alt={fullName}
                    className="w-24 h-32 object-cover border border-gray-300"
                    height={128}
                    src={resume.photo}
                    width={96}
                  />
                </div>
              ) : null}

              {/* Name, Title and Contact Info */}
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-1">{fullName}</h1>
                <p className="text-lg text-gray-700 mb-4">{resume.jobTitle}</p>

                {/* Contact Information with red icons */}
                <div className="contact-section space-y-1 text-sm text-gray-700">
                  {location && (
                    <OnyxContact icon="📍">
                      <span>{location}</span>
                    </OnyxContact>
                  )}
                  {resume.phone && (
                    <OnyxContact icon="📞">
                      <span>{resume.phone}</span>
                    </OnyxContact>
                  )}
                  {resume.email && (
                    <OnyxContact icon="✉️">
                      <span>{resume.email}</span>
                    </OnyxContact>
                  )}
                  {resume.website && (
                    <OnyxContact icon="🔗">
                      <span>{resume.website}</span>
                    </OnyxContact>
                  )}
                </div>
              </div>
            </div>

            {/* Right side: Social Media Links */}
            {resume.profiles && resume.profiles.length > 0 && (
              <div className="flex flex-col items-end">
                <SocialProfile
                  className="text-blue-600"
                  layout="vertical"
                  profiles={resume.profiles}
                  showNetworkLabel={false}
                />
              </div>
            )}
          </div>
        </header>

        {/* Main Content */}
        <main className="space-y-6">
          {/* Summary */}
          {resume.bio && (
            <OnyxSection title={sectionTitles.summary}>
              <ProfessionalSummary
                bio={resume.bio}
                className="text-sm text-gray-700 leading-relaxed"
                variant="paragraph"
              />
            </OnyxSection>
          )}

          {/* Experience */}
          {resume.experiences && resume.experiences.length > 0 && (
            <OnyxSection title={sectionTitles.experience}>
              <div className="space-y-6">
                {resume.experiences.map((experience) => (
                  <div key={experience.id} className="mb-4">
                    <ExperienceItem
                      className="[&_.text-blue-500]:text-red-600"
                      experience={experience}
                      locale={locale}
                      showWebsiteIcon={true}
                      variant="standard"
                    />
                  </div>
                ))}
              </div>
            </OnyxSection>
          )}

          {/* Education */}
          {resume.educations && resume.educations.length > 0 && (
            <OnyxSection title={sectionTitles.education}>
              <div className="space-y-4">
                {resume.educations.map((education) => (
                  <EducationItem
                    key={education.id}
                    className="mb-4"
                    education={education}
                    locale={locale}
                    variant="standard"
                  />
                ))}
              </div>
            </OnyxSection>
          )}

          {/* Projects */}
          {resume.projects && resume.projects.length > 0 && (
            <OnyxSection title={sectionTitles.projects}>
              <div className="space-y-4">
                {resume.projects.map((project) => (
                  <ProjectItem
                    key={project.id}
                    className="mb-4"
                    project={project}
                    showClient={true}
                    showTechnologies={false}
                    variant="standard"
                  />
                ))}
              </div>
            </OnyxSection>
          )}

          {/* Certifications */}
          {resume.certifications && resume.certifications.length > 0 && (
            <OnyxSection title={sectionTitles.certifications}>
              <div className="grid grid-cols-2 gap-6">
                {resume.certifications.map((certification) => (
                  <CertificationItem
                    key={certification.id}
                    certification={certification}
                    className=""
                    showCredentialId={false}
                  />
                ))}
              </div>
            </OnyxSection>
          )}

          {/* Skills */}
          {resume.skills && resume.skills.length > 0 && (
            <OnyxSection title={sectionTitles.skills}>
              <div className="grid grid-cols-3 gap-6">
                {(() => {
                  const skillsByCategory = resume.skills.reduce(
                    (acc, skill) => {
                      const category = skill.category || "";
                      if (!acc[category]) acc[category] = [];
                      acc[category].push(skill);
                      return acc;
                    },
                    {} as Record<string, typeof resume.skills>,
                  );

                  return Object.entries(skillsByCategory).map(([category, categorySkills]) => (
                    <div key={category}>
                      <h4 className="font-bold text-gray-900 mb-2 text-sm">{category}</h4>
                      <p className="text-gray-600 text-sm mb-1">Advanced</p>
                      <p className="text-gray-700 text-sm leading-relaxed">
                        {categorySkills.map((skill) => skill.name).join(", ")}
                      </p>
                    </div>
                  ));
                })()}
              </div>
            </OnyxSection>
          )}

          {/* References */}
          <OnyxSection title={sectionTitles.references}>
            <p className="text-gray-700 text-sm">Available upon request</p>
          </OnyxSection>
        </main>
      </div>
    </div>
  );
};

export default OnyxTemplate;
