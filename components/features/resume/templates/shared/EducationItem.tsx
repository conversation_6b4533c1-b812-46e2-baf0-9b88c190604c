import React from "react";
import { Education } from "@/db/schema";
import { formatDateRange, formatLocation } from "./utils";

export interface EducationItemProps {
  education: Education;
  variant?: "standard" | "compact" | "europass";
  className?: string;
  locale?: string;
}

export const EducationItem: React.FC<EducationItemProps> = ({
  education,
  variant = "standard",
  className = "",
  locale = "en-US",
}) => {
  const location = formatLocation(education.city || "", education.country || "");
  const dateRange = formatDateRange(
    education.startDate || "",
    education.endDate || "",
    education.isCurrent || 0,
    locale,
  );
  const degreeText = education.fieldOfStudy ? `${education.degree} in ${education.fieldOfStudy}` : education.degree;

  if (variant === "europass") {
    return (
      <div className={`education-item mb-4 flex ${className}`}>
        <div className="w-1/3 text-sm text-blue-500 font-medium pr-4">{dateRange}</div>
        <div className="flex-1">
          <h3 className="font-bold text-gray-900">{degreeText}</h3>
          <p className="text-gray-700 font-medium">{education.institution}</p>
          {location && <p className="text-gray-600 text-sm">{location}</p>}
          {education.description && (
            <div dangerouslySetInnerHTML={{ __html: education.description }} className="text-gray-700 text-sm mt-2" />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`education-item mb-4 ${className}`}>
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-bold text-gray-900">{education.institution}</h3>
          <p className="text-gray-700 text-sm">{degreeText}</p>
          {location && <p className="text-gray-600 text-sm">{location}</p>}
        </div>
        <div className="text-right text-sm text-gray-600">
          <p>{dateRange}</p>
        </div>
      </div>
      {education.description && (
        <div dangerouslySetInnerHTML={{ __html: education.description }} className="text-gray-700 text-sm mt-2" />
      )}
    </div>
  );
};
