import React from "react";
import { ResumeSectionProps } from "./types";

export const ResumeSection: React.FC<ResumeSectionProps> = ({
  title,
  children,
  variant = "default",
  accentColor = "#f97316", // orange-500
  className = "",
}) => {
  const baseClasses = "resume-section mb-6";

  const variantStyles = {
    default: "section-title text-lg font-bold mb-3 text-gray-900 border-b border-gray-300 pb-1",
    sidebar: "section-title text-sm font-bold mb-3 text-gray-700",
    accent: `section-title text-lg font-bold mb-4 flex items-center text-gray-800`,
    europass: "section-title text-lg font-bold mb-4 text-blue-600 border-b-2 border-blue-600 pb-2",
  };

  return (
    <section className={`${baseClasses} ${className}`}>
      <h2 className={variantStyles[variant]} style={variant === "accent" ? { color: accentColor } : {}}>
        {variant === "accent" && (
          <span className="w-3 h-3 rounded-full me-3" style={{ backgroundColor: accentColor }} />
        )}
        {title}
      </h2>
      {children}
    </section>
  );
};
