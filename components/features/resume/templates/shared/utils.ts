import { useLocale, useTranslations } from "next-intl";

// Utility functions for ATS compatibility
export const formatDate = (
  dateString: string,
  showPresent: boolean = false,
  locale: string = "en-US",
  yearOnly: boolean = false,
): string => {
  if (!dateString && showPresent) {
    return locale === "ar" ? "حتى الآن" : "Present";
  }
  if (!dateString) return "";

  const date = new Date(dateString);

  if (yearOnly) {
    return date.getFullYear().toString();
  }

  if (locale === "ar") {
    return date.toLocaleDateString("ar-SA", {
      year: "numeric",
      month: "short",
    });
  }

  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
  });
};

export const formatDateRange = (
  startDate: string,
  endDate: string,
  isCurrent: number = 0,
  locale: string = "en-US",
): string => {
  const start = formatDate(startDate, false, locale);
  const end = isCurrent ? (locale === "ar" ? "حتى الآن" : "Present") : formatDate(endDate, false, locale);
  return `${start} - ${end}`;
};

export const getFullName = (firstName: string, lastName: string, locale: string = "en-US"): string => {
  // In Arabic, sometimes family name comes first
  if (locale === "ar" && lastName && firstName) {
    return `${firstName} ${lastName}`.trim();
  }
  return `${firstName} ${lastName}`.trim();
};

export const formatLocation = (city: string, country: string): string => {
  const parts = [city, country].filter(Boolean);
  return parts.join(", ");
};

// Hook to get current locale for templates
export const useTemplateLocale = () => {
  try {
    const locale = useLocale();
    return locale;
  } catch {
    // Fallback for server-side rendering
    return "en";
  }
};

export const useSectionTranslations = () => {
  const t = useTranslations("forms");
  return {
    summary: t("summary"),
    experience: t("experience"),
    education: t("education"),
    skills: t("skills"),
    languages: t("languages"),
    projects: t("projects"),
    certifications: t("certifications"),
    profiles: t("profiles"),
    references: t("references"),
    volunteering: t("volunteering"),
    awards: t("awards"),
    hobbies: t("hobbies"),
  };
};
