import React from "react";

export interface ContactInfoProps {
  location?: string;
  phone?: string;
  email: string;
  website?: string;
  variant?: "horizontal" | "vertical" | "compact";
  className?: string;
}

export const ContactInfo: React.FC<ContactInfoProps> = ({
  location,
  phone,
  email,
  website,
  variant = "vertical",
  className = "",
}) => {
  const contactItems = [
    { icon: "📍", value: location, href: null },
    { icon: "📞", value: phone, href: phone ? `tel:${phone}` : null },
    { icon: "✉️", value: email, href: email ? `mailto:${email}` : null },
    { icon: "🌐", value: website, href: website },
  ].filter((item) => item.value);

  const containerClass = variant === "horizontal" ? "flex flex-wrap gap-4" : "space-y-2";

  return (
    <div className={`contact-section ${containerClass} ${className}`}>
      {contactItems.map((item, index) => (
        <div key={index} className="flex items-center text-sm">
          <span className="me-2">{item.icon}</span>
          {item.href ? (
            <a className="text-blue-600 hover:text-blue-800" href={item.href}>
              {item.value}
            </a>
          ) : (
            <span>{item.value}</span>
          )}
        </div>
      ))}
    </div>
  );
};
