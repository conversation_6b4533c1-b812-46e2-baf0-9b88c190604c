import React from "react";
import { Experience } from "@/db/schema";
import { formatDateRange, formatLocation } from "./utils";

export interface ExperienceItemProps {
  experience: Experience;
  variant?: "standard" | "compact" | "detailed" | "europass";
  showWebsiteIcon?: boolean;
  className?: string;
  locale?: string;
}

export const ExperienceItem: React.FC<ExperienceItemProps> = ({
  experience,
  variant = "standard",
  showWebsiteIcon: _showWebsiteIcon = true,
  className = "",
  locale = "en-US",
}) => {
  const location = formatLocation(experience.city || "", experience.country || "");
  const dateRange = formatDateRange(experience.startDate, experience.endDate, experience.isCurrent, locale);

  if (variant === "europass") {
    return (
      <div className={`experience-item mb-4 flex ${className}`}>
        <div className="w-1/3 text-sm text-blue-500 font-medium pr-4">{dateRange}</div>
        <div className="flex-1">
          <h3 className="font-bold text-gray-900">{experience.title}</h3>
          <p className="text-gray-700 font-medium">{experience.company}</p>
          {location && <p className="text-gray-600 text-sm">{location}</p>}
          {experience.description && (
            <div dangerouslySetInnerHTML={{ __html: experience.description }} className="text-gray-700 text-sm mt-2" />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`experience-item mb-4 ${className}`}>
      <div className="flex justify-between items-start mb-2">
        <div className="flex-1">
          <h3 className="font-bold text-gray-900 text-base">{experience.company}</h3>
          <p className="text-gray-700 text-sm font-semibold">{experience.title}</p>
        </div>
        <div className="text-right text-sm text-gray-600">
          <p>{dateRange}</p>
          {location && <p>{location}</p>}
        </div>
      </div>
      {experience.description && (
        <div dangerouslySetInnerHTML={{ __html: experience.description }} className="text-gray-700 text-sm mt-2" />
      )}
    </div>
  );
};
