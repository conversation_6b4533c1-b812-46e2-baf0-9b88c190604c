import React from "react";
import {
  CertificationItem,
  EducationItem,
  ExperienceItem,
  formatLocation,
  LanguageItem,
  ProfessionalSummary,
  ProjectItem,
  ResumeHeader,
  ResumeSection,
  SkillsSection,
  SocialProfile,
  TemplateProps,
  useSectionTranslations,
  useTemplateLocale,
} from "./shared";

// Custom contact component for this template's green dot style
const BronzorContact: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="flex items-center">
    <span className="w-2 h-2 bg-green-500 rounded-full me-2" />
    {children}
  </div>
);

/**
 * Bronzor Resume Template - Clean Professional Style
 * - Minimal, clean design with horizontal section dividers
 * - Single column layout with excellent readability
 * - Professional typography and spacing
 * - ATS-friendly structure
 */
export const BronzorTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = useSectionTranslations();
  const location = formatLocation(resume.city, resume.country);

  return (
    <div className={`bronzor-template bg-white text-gray-900 font-sans ${className}`}>
      {/* Page wrapper with A4 proportions */}
      <div className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-white p-8">
        {/* Header with custom contact styling */}
        <ResumeHeader
          className="text-center mb-8"
          firstName={resume.firstName}
          jobTitle={resume.jobTitle}
          lastName={resume.lastName}
          layout="centered"
          locale={locale}
          photo={resume.photo}
          showPhoto={resume.showPhoto === 1}
        >
          {/* Contact info with green bullets */}
          <div className="flex justify-center items-center flex-wrap gap-x-6 gap-y-2 text-sm text-gray-600 contact-section">
            {location && (
              <BronzorContact>
                <span>{location}</span>
              </BronzorContact>
            )}
            {resume.phone && (
              <BronzorContact>
                <span>{resume.phone}</span>
              </BronzorContact>
            )}
            {resume.email && (
              <BronzorContact>
                <span>{resume.email}</span>
              </BronzorContact>
            )}
            {resume.website && (
              <BronzorContact>
                <span>{resume.website}</span>
              </BronzorContact>
            )}
          </div>
        </ResumeHeader>

        {/* Profiles Section */}
        {resume.profiles && resume.profiles.length > 0 && (
          <ResumeSection title={sectionTitles.profiles} variant="default">
            <SocialProfile
              className="grid-cols-1 md:grid-cols-3 gap-4"
              layout="grid"
              profiles={resume.profiles}
              showNetworkLabel={true}
            />
          </ResumeSection>
        )}

        {/* Summary Section */}
        {resume.bio && (
          <ResumeSection title={sectionTitles.summary} variant="default">
            <ProfessionalSummary bio={resume.bio} variant="paragraph" />
          </ResumeSection>
        )}

        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <ResumeSection title={sectionTitles.experience} variant="default">
            <div className="space-y-6">
              {resume.experiences.map((exp) => (
                <ExperienceItem
                  key={exp.id}
                  experience={exp}
                  locale={locale}
                  showWebsiteIcon={true}
                  variant="standard"
                />
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <ResumeSection title={sectionTitles.education} variant="default">
            <div className="space-y-4">
              {resume.educations.map((edu) => (
                <EducationItem key={edu.id} education={edu} locale={locale} variant="standard" />
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <ResumeSection title={sectionTitles.projects} variant="default">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {resume.projects.map((project) => (
                <ProjectItem
                  key={project.id}
                  project={project}
                  showClient={true}
                  showTechnologies={false}
                  variant="standard"
                />
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Skills Section */}
        {resume.skills && resume.skills.length > 0 && (
          <ResumeSection title={sectionTitles.skills} variant="default">
            <div className="">
              <SkillsSection skills={resume.skills} />
            </div>
          </ResumeSection>
        )}

        {/* Certifications Section */}
        {resume.certifications && resume.certifications.length > 0 && (
          <ResumeSection title={sectionTitles.certifications} variant="default">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {resume.certifications.map((cert) => (
                <CertificationItem key={cert.id} certification={cert} showCredentialId={false} />
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Languages Section */}
        {resume.languages && resume.languages.length > 0 && (
          <ResumeSection title={sectionTitles.languages} variant="default">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {resume.languages.map((lang) => (
                <LanguageItem key={lang.id} language={lang} showBars={false} showLevel={true} />
              ))}
            </div>
          </ResumeSection>
        )}

        {/* References Section */}
        <section className="mb-6 resume-section">
          <h2 className="text-lg font-bold text-gray-900 mb-3 pb-1 border-b border-gray-300 section-title">
            {sectionTitles.references}
          </h2>
          <p className="text-gray-600 text-sm">Available upon request</p>
        </section>
      </div>
    </div>
  );
};

export default BronzorTemplate;
