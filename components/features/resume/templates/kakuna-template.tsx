import Image from "next/image";
import React from "react";
import {
  ContactInfo,
  ExperienceItem,
  formatLocation,
  getFullName,
  ProfessionalSummary,
  SocialProfile,
  TemplateProps,
  useSectionTranslations,
  useTemplateLocale,
} from "./shared";

// Custom section component for kakuna template with centered styling
const KakunaSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="mb-6 resume-section">
    <h2 className="text-lg font-bold mb-3 text-gray-900 text-center border-b border-gray-300 pb-2 section-title">
      {title}
    </h2>
    {children}
  </section>
);

// Custom experience item for kakuna template with grid layout
const KakunaExperienceItem: React.FC<{
  experience: any;
  locale: string;
}> = ({ experience, locale }) => (
  <ExperienceItem
    className="experience-item mb-4"
    experience={experience}
    locale={locale}
    showWebsiteIcon={true}
    variant="standard"
  />
);

// Custom education item for kakuna template with centered layout
const KakunaEducationItem: React.FC<{
  education: any;
  locale: string;
}> = ({ education, locale }) => (
  <div className="mb-4 education-item text-center">
    <h3 className="font-bold text-gray-900">{education.institution}</h3>
    <p className="text-gray-700 text-sm">
      {education.degree}
      {education.field_of_study && ` in ${education.field_of_study}`}
    </p>
    <p className="text-gray-600 text-sm">
      {formatDateRange(education.startDate, education.endDate, education.isCurrent, locale)}
    </p>
    {education.description && (
      <div
        dangerouslySetInnerHTML={{ __html: education.description }}
        className="text-sm text-gray-700 leading-relaxed mt-2"
      />
    )}
  </div>
);

// Custom project item for kakuna template with grid layout
const KakunaProjectItem: React.FC<{
  project: any;
}> = ({ project }) => (
  <div className="project-item mb-4">
    <h3 className="font-bold text-gray-900 mb-1">{project.title}</h3>
    {project.client && <p className="text-gray-700 text-sm font-medium">{project.client}</p>}
    {project.description && <p className="text-gray-700 text-sm leading-relaxed mt-1">{project.description}</p>}
  </div>
);

// Custom certification item for kakuna template with centered layout
const KakunaCertificationItem: React.FC<{
  certification: any;
  locale: string;
}> = ({ certification, locale }) => (
  <div className="text-center certification-item">
    <h4 className="font-bold text-gray-900 text-sm">{certification.title}</h4>
    <p className="text-gray-700 text-sm">{certification.issuer}</p>
    <p className="text-gray-600 text-sm">
      {certification.dateReceived && formatDate(certification.dateReceived, false, locale)}
    </p>
  </div>
);

// Custom skills section for kakuna template with grid layout
const KakunaSkillsSection: React.FC<{
  skills: any[];
}> = ({ skills }) => {
  const skillsByCategory = skills.reduce(
    (acc, skill) => {
      const category = skill.category || "";
      if (!acc[category]) acc[category] = [];
      acc[category].push(skill);
      return acc;
    },
    {} as Record<string, any[]>,
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
        <div key={category} className="text-center">
          <h4 className="font-bold text-gray-900 mb-2 text-sm">{category}</h4>
          <p className="text-gray-600 text-sm mb-1">Advanced</p>
          <p className="text-gray-700 text-sm leading-relaxed">
            {(categorySkills as any[]).map((skill: any) => skill.name).join(", ")}
          </p>
        </div>
      ))}
    </div>
  );
};

// Import formatDate for certifications
const formatDate = (dateString: string, showPresent: boolean = false, locale: string = "en-US"): string => {
  if (!dateString && showPresent) {
    return locale === "ar" ? "حتى الآن" : "Present";
  }
  if (!dateString) return "";

  const date = new Date(dateString);

  if (locale === "ar") {
    return date.toLocaleDateString("ar-SA", {
      year: "numeric",
      month: "short",
    });
  }

  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
  });
};

const formatDateRange = (
  startDate: string,
  endDate: string,
  isCurrent: boolean = false,
  locale: string = "en-US",
): string => {
  const start = formatDate(startDate, false, locale);
  const end = isCurrent ? (locale === "ar" ? "حتى الآن" : "Present") : formatDate(endDate, false, locale);
  return `${start} - ${end}`;
};

/**
 * Kakuna Resume Template - Clean Centered Layout
 * - Centered photo and contact info header
 * - Clean single-column layout with centered sections
 * - Side-by-side experience layout for multiple positions
 * - Simple typography and minimal styling
 * - Professional appearance matching reference design
 * - ATS-friendly structure with proper hierarchy
 */
export const KakunaTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = useSectionTranslations();
  const fullName = getFullName(resume.firstName, resume.lastName, locale);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div className={`kakuna-template bg-white text-gray-900 font-sans ${className}`}>
      {/* Page wrapper with A4 proportions */}
      <div className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-white p-8">
        {/* Centered Header */}
        <header className="text-center mb-8 resume-header">
          {/* Photo */}
          {resume.showPhoto && resume.photo ? (
            <div className="mb-4">
              <Image
                alt={fullName}
                className="w-24 h-32 object-cover mx-auto border border-gray-300"
                height={128}
                src={resume.photo}
                width={96}
              />
            </div>
          ) : null}

          {/* Name and Title */}
          <h1 className="text-2xl font-bold text-gray-900 mb-1">{fullName}</h1>
          <p className="text-lg text-gray-700 mb-4">{resume.jobTitle}</p>

          {/* Contact Information */}
          <ContactInfo
            className="mb-4 justify-center flex-wrap gap-x-6 gap-y-2 text-sm text-gray-700"
            email={resume.email}
            location={location}
            phone={resume.phone}
            variant="horizontal"
            website={resume.website}
          />

          {/* Social Media Links */}
          {resume.profiles && resume.profiles.length > 0 && (
            <SocialProfile
              className="justify-center space-x-4"
              layout="horizontal"
              profiles={resume.profiles}
              showNetworkLabel={false}
            />
          )}
        </header>

        {/* Main Content */}
        <main className="space-y-6">
          {/* Summary */}
          {resume.bio && (
            <KakunaSection title={sectionTitles.summary}>
              <ProfessionalSummary bio={resume.bio} className="text-center max-w-4xl mx-auto" variant="paragraph" />
            </KakunaSection>
          )}

          {/* Experience */}
          {resume.experiences && resume.experiences.length > 0 && (
            <KakunaSection title={sectionTitles.experience}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {resume.experiences.map((exp) => (
                  <KakunaExperienceItem key={exp.id} experience={exp} locale={locale} />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Education */}
          {resume.educations && resume.educations.length > 0 && (
            <KakunaSection title={sectionTitles.education}>
              <div className="text-center">
                {resume.educations.map((edu) => (
                  <KakunaEducationItem key={edu.id} education={edu} locale={locale} />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Projects */}
          {resume.projects && resume.projects.length > 0 && (
            <KakunaSection title={sectionTitles.projects}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {resume.projects.map((project) => (
                  <KakunaProjectItem key={project.id} project={project} />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Certifications */}
          {resume.certifications && resume.certifications.length > 0 && (
            <KakunaSection title={sectionTitles.certifications}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {resume.certifications.map((cert) => (
                  <KakunaCertificationItem key={cert.id} certification={cert} locale={locale} />
                ))}
              </div>
            </KakunaSection>
          )}

          {/* Skills */}
          {resume.skills && resume.skills.length > 0 && (
            <KakunaSection title={sectionTitles.skills}>
              <KakunaSkillsSection skills={resume.skills} />
            </KakunaSection>
          )}

          {/* References */}
          <KakunaSection title={sectionTitles.references}>
            <p className="text-gray-700 text-sm text-center">Available upon request</p>
          </KakunaSection>
        </main>
      </div>
    </div>
  );
};

export default KakunaTemplate;
