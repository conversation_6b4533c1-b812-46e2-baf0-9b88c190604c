"use client";

import { trpc } from "@/app/_trpc/client";
import { ResumeCardSkeleton } from "@/components/shared/skeleton-loader";
import { useFilterSort } from "@/hooks/use-filter-sort";
import { exportResumes } from "@/lib/services/export-service";
import ResumeCard from "./resume-card";
import ResumeEmptyState from "./resume-empty-state";
import ResumePageHeader from "./resume-page-header";

export default function ResumeViewContainer() {
  const getResumes = trpc.resumes.getResumes.useQuery();
  const { isLoading, data: resumes } = getResumes;

  // Use the custom hook for filtering and sorting
  const {
    filter,
    setFilter,
    sort,
    setSort,
    filteredAndSortedItems: filteredAndSortedResumes,
    resetFilters,
  } = useFilterSort(resumes, {
    filterFn: (resume, filterValue): boolean => {
      switch (filterValue) {
        case "recent": {
          const createdDate = new Date(resume.createdAt);
          const daysSinceCreation = (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24);
          return daysSinceCreation <= 7;
        }
        case "favorites":
          // For now, we'll show all as we don't have a favorites field yet
          // TODO: Add favorites functionality
          return true;
        case "completed":
          // Show resumes with all required fields filled
          return !!(resume.firstName && resume.lastName && resume.email && resume.jobTitle);
        case "draft":
          // Show resumes with missing required fields
          return !resume.firstName || !resume.lastName || !resume.email || !resume.jobTitle;
        default:
          return true;
      }
    },
    getItemName: (resume) => resume.title || "",
  });

  // Export all resumes handler
  const handleExportAll = async () => {
    await exportResumes(filteredAndSortedResumes);
  };

  if ((!resumes || resumes.length === 0) && !isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <ResumeEmptyState />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <ResumePageHeader
        resumeCount={resumes?.length || 0}
        onFilterChange={setFilter}
        onSortChange={setSort}
        onExportAll={handleExportAll}
        onReset={resetFilters}
        currentFilter={filter}
        currentSort={sort}
        filteredCount={filteredAndSortedResumes.length}
      />

      {/* Resume Grid */}
      {isLoading ? (
        <ResumeCardSkeleton count={6} />
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 justify-items-center">
          {filteredAndSortedResumes.map((resume) => (
            <div key={resume.id} className="w-full max-w-[280px]">
              <ResumeCard resume={resume} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
