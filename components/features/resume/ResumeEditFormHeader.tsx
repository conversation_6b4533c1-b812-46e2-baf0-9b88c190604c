"use client";

import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";

export const ResumeEditFormHeader = () => {
  const t = useTranslations();

  return (
    <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
      <div className="flex items-center space-x-3">
        <div className="w-6 h-6 rounded-lg bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
          <Icon icon="lucide:file-edit" className="w-4 h-4 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t("resume.editor.edit_details")}</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">{t("resume.editor.edit_details_description")}</p>
        </div>
      </div>
    </div>
  );
};
