"use client";

import { useRouter } from "@bprogress/next/app";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  Input,
  Modal,
  ModalBody,
  <PERSON>dalContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>dal<PERSON>eader,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { useRoutes } from "@/config/path-constants";
import { useTRPCLoading } from "@/lib/use-loading-action";
import type { DeleteResumeResponse, DuplicateResumeResponse, RenameResumeResponse } from "@/types/trpc-responses";

interface ResumeCardActionsProps {
  resumeId: number;
}

export default function ResumeCardActions({ resumeId }: ResumeCardActionsProps) {
  const t = useTranslations("resumes.actions");
  const router = useRouter();
  const { resumeEditPath } = useRoutes();
  const [newTitle, setNewTitle] = useState("");
  const { isOpen, onOpen, onClose } = useDisclosure();
  const deleteResumeMutation = trpc.resumes.deleteResume.useMutation();
  const duplicateResumeMutation = trpc.resumes.duplicateResume.useMutation();
  const renameResumeMutation = trpc.resumes.renameResume.useMutation();
  const { wrapTRPCMutation } = useTRPCLoading();

  // Wrap TRPC mutations with global loading
  const wrappedDuplicate = wrapTRPCMutation(duplicateResumeMutation, `duplicate-resume-${resumeId}`, t("duplicating"));

  const wrappedRename = wrapTRPCMutation(renameResumeMutation, `rename-resume-${resumeId}`, t("renaming"));

  const wrappedDelete = wrapTRPCMutation(deleteResumeMutation, `delete-resume-${resumeId}`, t("deleting"));

  const handleDuplicate = async () => {
    try {
      const result = (await wrappedDuplicate.mutateAsync({
        id: resumeId,
      })) as DuplicateResumeResponse;
      if (result.success) {
        toast.success(t("duplicate_success"));
      } else {
        toast.error(t("duplicate_failed"));
      }
    } catch (error) {
      console.error("Error duplicating resume:", error);
      toast.error(t("duplicate_failed"));
    }
  };

  const handleRename = async () => {
    if (!newTitle.trim()) return;

    // Close modal immediately and show global loader
    onClose();

    try {
      const result = (await wrappedRename.mutateAsync({
        id: resumeId,
        title: newTitle.trim(),
      })) as RenameResumeResponse;

      if (result.success) {
        setNewTitle("");
        toast.success(t("rename_success"));
      } else {
        toast.error(t("rename_failed"));
      }
    } catch (error) {
      console.error("Error renaming resume:", error);
      toast.error(t("rename_failed"));
    }
  };

  const handleDelete = async () => {
    try {
      const result = (await wrappedDelete.mutateAsync({
        id: resumeId,
      })) as DeleteResumeResponse;
      if (result.success) {
        toast.success(t("delete_success"));
      } else {
        toast.error(t("delete_failed"));
      }
    } catch (error) {
      console.error("Error deleting resume:", error);
      toast.error(t("delete_failed"));
    }
  };

  return (
    <>
      <Dropdown>
        <DropdownTrigger>
          <Button
            isIconOnly
            aria-label={t("more_options")}
            className="transition-all duration-200 hover:scale-105"
            size="sm"
            variant="light"
            color="default"
          >
            <Icon height="16" icon="lucide:more-horizontal" width="16" />
          </Button>
        </DropdownTrigger>
        <DropdownMenu aria-label="Resume actions">
          <DropdownItem
            key="edit"
            startContent={<Icon className="w-4 h-4" icon="lucide:edit-3" />}
            onPress={() => router.push(resumeEditPath(resumeId))}
          >
            {t("edit")}
          </DropdownItem>

          <DropdownItem key="rename" startContent={<Icon className="w-4 h-4" icon="lucide:edit" />} onPress={onOpen}>
            {t("rename")}
          </DropdownItem>

          <DropdownItem
            key="duplicate"
            startContent={<Icon className="w-4 h-4" icon="lucide:copy" />}
            onPress={handleDuplicate}
          >
            {t("duplicate")}
          </DropdownItem>

          <DropdownItem
            key="delete"
            className="text-danger"
            onPress={handleDelete}
            color="danger"
            startContent={<Icon className="w-4 h-4" icon="lucide:trash-2" />}
          >
            {t("delete")}
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>

      <Modal isOpen={isOpen} onClose={onClose} placement="top-center">
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">{t("rename_modal_title")}</ModalHeader>
          <ModalBody>
            <Input
              autoFocus
              label={t("resume_title")}
              placeholder={t("enter_new_title")}
              value={newTitle}
              onValueChange={setNewTitle}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleRename();
                }
              }}
            />
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onClose}>
              {t("cancel")}
            </Button>
            <Button color="primary" onPress={handleRename} isDisabled={!newTitle.trim()}>
              {t("save")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
