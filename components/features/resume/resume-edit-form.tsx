"use client";

import { Accordion, AccordionI<PERSON>, Button, Form } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useMemo } from "react";
import { Autosave } from "react-autosave";
import { trpc } from "@/app/_trpc/client";
import LoaderComponent from "@/components/shared/common/LoaderComponent";
import { useSchemas } from "@/config/schemas-i18n";
import { prepareNestedForms, RenderForm } from "@/lib/form";
import { useFormStore } from "@/lib/form-store";
import { upperCaseFirstLetter } from "@/lib/utils";
import { AccordionFormList } from "../../forms";
import SpinnerComponent from "../../shared/common/spinner";
import PersonalFormFields from "./personalFormFields";

export const ResumeEditForm = ({ resumeId }: { resumeId: number }) => {
  const schemas = useSchemas();
  const t = useTranslations();
  const { formData, setFormData } = useFormStore();
  const getFullResume = trpc.resumes.getFullResume.useQuery({ id: resumeId });
  const updateResumeMutation = trpc.resumes.updateResume.useMutation();

  const { isLoading, data: initialData } = getFullResume;

  // Use formData if available, otherwise use initialData
  const currentData = formData || initialData;
  const nested_forms = useMemo(() => prepareNestedForms(currentData, schemas), [currentData, schemas]);

  // Populate form store when initial data is loaded
  useEffect(() => {
    if (initialData && !formData) {
      setFormData(initialData);
    }
  }, [initialData, formData]);

  // Memoize the renderForm function to prevent unnecessary re-renders and focus loss
  const renderFormCallback = useCallback(
    (item: any, index: number, schema: any) => (
      <RenderForm index={index} item={item} schema={schema} resumeData={currentData} />
    ),
    [currentData],
  );

  const addEmptyItemToForm = (form: any) => {
    if (!form || !initialData) return;

    const collectionName = form.schema.collection;
    const currentItems = (initialData[collectionName as keyof typeof initialData] || []) as unknown[];
    const properties = form.properties;
    const newEmptyItem = {
      ...properties.reduce((acc: any, property: any) => {
        acc[property] = "";
        return acc;
      }, {}),
      sort: currentItems.length,
      resumeId: resumeId,
    };

    const updatedItems = [...currentItems, newEmptyItem];
    setFormData({
      ...initialData,
      [collectionName]: updatedItems,
    });
  };

  const handleSubmit = async (_data: FormData) => {
    if (!formData) return;

    try {
      await updateResumeMutation.mutateAsync({
        ...formData,
        id: resumeId,
      });
    } catch (error) {
      console.error("Failed to save form data:", error);
    }
  };

  if (isLoading) return <LoaderComponent />;

  return (
    <div className="flex flex-col gap-6 w-full p-6">
      <Autosave interval={1000} data={formData} onSave={() => handleSubmit(new FormData())} />
      <Form action={handleSubmit} className="w-full">
        {/* Personal Information Section */}
        <div className="space-y-6 w-full">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-3 rtl:gap-x-reverse mb-4">
              <div className="w-8 h-8 rounded-lg bg-blue-500 flex items-center justify-center">
                <Icon icon="lucide:user" className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">{t("resume.personal_information")}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {t("resume.personal_information_description")}
                </p>
              </div>
            </div>
            <PersonalFormFields data={currentData} />
          </div>
        </div>

        {/* Resume Sections */}
        <div className="space-y-4 mt-6 w-full">
          <div className="flex items-center gap-3 rtl:gap-x-reverse mb-6">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
              <Icon icon="lucide:file-text" className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t("resume.sections")}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">{t("resume.sections_description")}</p>
            </div>
          </div>

          {nested_forms.map((form, formIndex) => {
            const itemKey = `${form.name}-${formIndex}`;
            const sectionIcons = {
              experience: "lucide:briefcase",
              education: "lucide:graduation-cap",
              skills: "lucide:zap",
              projects: "lucide:folder",
              award: "lucide:award",
              certification: "lucide:certificate",
              language: "lucide:globe",
              reference: "lucide:users",
              hobby: "lucide:heart",
              volunteering: "lucide:hand-heart",
              profile: "lucide:link",
              default: "lucide:file-text",
            };

            const iconName = sectionIcons[form.schema?.entity as keyof typeof sectionIcons] || sectionIcons.default;

            return (
              <div key={itemKey} className="mb-4 w-full">
                <Accordion isCompact variant="splitted" className="shadow-sm w-full">
                  <AccordionItem
                    key={itemKey}
                    aria-label={form.schema?.collection || form.name}
                    className="w-full group"
                    classNames={{
                      base: "w-full px-0",
                      heading: "w-full",
                      title: "w-full flex-1 text-medium",
                      titleWrapper: "flex-1",
                      content: "w-full pt-0 pb-4",
                      trigger: "w-full px-2 py-4 flex items-center justify-between gap-4",
                      indicator: "text-medium",
                    }}
                    indicator={
                      <Icon icon="lucide:chevron-right" className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    }
                    startContent={
                      <div className="flex items-center gap-3 rtl:gap-x-reverse">
                        <div className="w-6 h-6 rounded-md bg-gray-100 dark:bg-gray-700 flex items-center justify-center flex-shrink-0">
                          <Icon icon={iconName} className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                        </div>
                        {addItemButton({
                          onAddNew: () => addEmptyItemToForm(form),
                        })}
                      </div>
                    }
                    subtitle={form.schema?.description || ""}
                    textValue={form.schema?.entity || form.name}
                    title={
                      <div className="flex items-center justify-between w-full pe-2">
                        <span className="font-medium">
                          {upperCaseFirstLetter(t(`forms.${form.schema?.entity || form.name}`))}
                        </span>
                        <div className="text-xs text-gray-500 dark:text-gray-400 ms-auto me-2">
                          {form.items?.filter((item) => item.id != null).length || 0} {t("resume.items")}
                        </div>
                      </div>
                    }
                  >
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                      <AccordionFormList
                        className="p-4"
                        collectionName={form.schema?.collection}
                        items={
                          form.items
                            ?.filter((item) => item.id != null)
                            .map((item, index) => ({
                              ...item,
                              id: item.id!,
                              sort: index,
                            })) || []
                        }
                        keyName={form.keyName}
                        renderForm={(item, index) => renderFormCallback(item, index, form.schema)}
                        resumeId={resumeId}
                        titlePrefix={t(`forms.singular.${form.schema?.entity || form.name}`)}
                      />
                    </div>
                  </AccordionItem>
                </Accordion>
              </div>
            );
          })}
        </div>
      </Form>
    </div>
  );
};

const addItemButton = ({ onAddNew }: { onAddNew: () => void }) => {
  return (
    <Button
      isIconOnly
      color="primary"
      size="sm"
      startContent={<Icon icon="lucide:plus" />}
      variant="flat"
      className="bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 border border-blue-200 dark:border-blue-800 shadow-sm transition-all duration-200"
      onPress={onAddNew}
    />
  );
};
