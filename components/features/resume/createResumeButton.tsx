"use client";

import { useRouter } from "@bprogress/next/app";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  Form,
  Input,
  Modal,
  ModalBody,
  ModalContent,
  ModalHeader,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import toast from "react-hot-toast";
import { trpc } from "@/app/_trpc/client";
import { routes } from "@/config/path-constants";
import { useTRPCLoading } from "@/lib/use-loading-action";
import type { CreateResumeResponse } from "@/types/trpc-responses";
import { LinkedInCreateResumeButton } from "./linkedin-create-resume-button";
import { PDFCreateResumeButton } from "./pdf-create-resume-button";

export default function CreateResumeButton({
  className,
  size,
  text,
}: {
  className?: string;
  size?: "lg" | "md" | "sm";
  text?: string;
}) {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [newResumeId, setNewResumeId] = useState<number | null>(null);
  const [modalType, setModalType] = useState<"create" | "linkedin" | "pdf">("create");

  const t = useTranslations("resumes");
  const router = useRouter();
  const locale = useLocale();
  const createResumeMutation = trpc.resumes.createResume.useMutation();
  const { wrapTRPCMutation } = useTRPCLoading();

  // Wrap the TRPC mutation with global loading
  const wrappedCreateResume = wrapTRPCMutation(createResumeMutation, "create-resume", t("creating_resume"));

  const handleCreateNew = () => {
    setNewResumeId(null);
    setModalType("create");
    onOpen();
  };

  const handleImportLinkedIn = () => {
    setNewResumeId(null);
    setModalType("linkedin");
    onOpen();
  };

  const handleImportPDF = () => {
    setNewResumeId(null);
    setModalType("pdf");
    onOpen();
  };

  return (
    <>
      <Dropdown>
        <DropdownTrigger>
          <Button
            className={className}
            color="primary"
            radius="full"
            size={size}
            variant="shadow"
            endContent={<Icon icon="lucide:chevron-down" className="w-4 h-4" />}
          >
            {text || t("get_started")}
          </Button>
        </DropdownTrigger>
        <DropdownMenu aria-label="Create resume options">
          <DropdownItem
            key="create-new"
            startContent={<Icon className="w-4 h-4" icon="lucide:file-plus" />}
            onPress={handleCreateNew}
          >
            {t("create_new")}
          </DropdownItem>
          <DropdownItem
            key="import-linkedin"
            startContent={<Icon className="w-4 h-4" icon="simple-icons:linkedin" />}
            onPress={handleImportLinkedIn}
          >
            {t("import_from_linkedin")}
          </DropdownItem>
          <DropdownItem
            key="import-pdf"
            startContent={<Icon className="w-4 h-4" icon="tabler:file-type-pdf" />}
            onPress={handleImportPDF}
          >
            {t("import_from_pdf")}
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1 justify-center items-center">
                {modalType === "create" && t("create_resume")}
                {modalType === "linkedin" && t("import_from_linkedin")}
                {modalType === "pdf" && t("import_from_pdf")}
              </ModalHeader>
              <ModalBody>
                {modalType === "create" && !newResumeId && (
                  // Show resume creation form
                  <Form
                    action={async (formData) => {
                      const title = formData.get("title") as string;

                      if (!title?.trim()) {
                        toast.error(t("title_required"));
                        return;
                      }

                      try {
                        const result = (await wrappedCreateResume.mutateAsync({
                          title: title.trim(),
                        })) as CreateResumeResponse;

                        if (result.success) {
                          toast.success(t("resume_created"));
                          onClose();
                          router.push(routes.resumeEditPath(result.resumeId, locale));
                        } else {
                          toast.error(t("failed_to_create"));
                        }
                      } catch (error) {
                        console.error("Failed to create resume:", error);
                        toast.error(t("failed_to_create"));
                      }
                    }}
                    className="flex flex-col gap-4 p-4"
                  >
                    <Input
                      label={t("title")}
                      name="title"
                      placeholder={t("enter_resume_title")}
                      type="text"
                      variant="bordered"
                    />
                    <Button
                      className="w-full self-center"
                      color="primary"
                      size="sm"
                      type="submit"
                      isLoading={createResumeMutation.isPending}
                      isDisabled={createResumeMutation.isPending}
                    >
                      {t("create")}
                    </Button>
                  </Form>
                )}

                {modalType === "linkedin" && (
                  <LinkedInCreateResumeButton
                    onSuccess={(resumeId) => {
                      onClose();
                      router.push(routes.resumeEditPath(resumeId, locale));
                    }}
                  />
                )}

                {modalType === "pdf" && (
                  <PDFCreateResumeButton
                    onSuccess={(resumeId) => {
                      onClose();
                      router.push(routes.resumeEditPath(resumeId, locale));
                    }}
                  />
                )}
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
