"use client";

import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";

export const TemplatesPageHeader = () => {
  const t = useTranslations();

  return (
    <div className="text-center mb-12">
      <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 mb-6">
        <Icon icon="lucide:layout-template" className="w-8 h-8 text-white" />
      </div>
      <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent mb-4">
        {t("templates.page_header_title")}
      </h1>
      <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
        {t("templates.page_header_description")}
      </p>

      {/* Stats */}
      <div className="flex justify-center items-center space-x-8 mt-8">
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">12+</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">{t("templates.stats.templates")}</div>
        </div>
        <div className="w-px h-8 bg-gray-300 dark:bg-gray-600"></div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">ATS</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">{t("templates.stats.optimized")}</div>
        </div>
        <div className="w-px h-8 bg-gray-300 dark:bg-gray-600"></div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">100%</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">{t("templates.stats.customizable")}</div>
        </div>
      </div>
    </div>
  );
};
