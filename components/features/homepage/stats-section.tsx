"use client";
import { Card, CardBody } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { subtitle, title } from "@/components/primitives";

const stats = [
  {
    key: "resumes_created",
    value: "50,000+",
    labelKey: "stats.resumes_created",
    icon: "heroicons:document-text",
    color: "text-blue-600",
    bgColor: "bg-blue-100 dark:bg-blue-900/20",
  },
  {
    key: "job_success_rate",
    value: "94%",
    labelKey: "stats.job_success_rate",
    icon: "heroicons:check-circle",
    color: "text-green-600",
    bgColor: "bg-green-100 dark:bg-green-900/20",
  },
  {
    key: "templates_available",
    value: "12",
    labelKey: "stats.templates_available",
    icon: "heroicons:chart-bar",
    color: "text-purple-600",
    bgColor: "bg-purple-100 dark:bg-purple-900/20",
  },
  {
    key: "languages_supported",
    value: "2",
    labelKey: "stats.languages_supported",
    icon: "heroicons:globe-alt",
    color: "text-orange-600",
    bgColor: "bg-orange-100 dark:bg-orange-900/20",
  },
];

export default function StatsSection() {
  const t = useTranslations("homepage");

  return (
    <section className="py-20 md:py-32 bg-gradient-to-br from-default-50 via-default-100 to-default-50 dark:from-default-900/10 dark:via-default-800/20 dark:to-default-900/10 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-10 w-72 h-72 bg-primary-100 dark:bg-primary-900/10 rounded-full blur-3xl opacity-30" />
        <div className="absolute bottom-1/4 right-10 w-80 h-80 bg-secondary-100 dark:bg-secondary-900/10 rounded-full blur-3xl opacity-20" />
      </div>

      <div className="max-w-6xl mx-auto px-4 relative z-10">
        <div className="text-center mb-20">
          <h2 className={title({ size: "md" })}>{t("stats.section_title")}</h2>
          <p className={subtitle({ class: "mt-6 max-w-3xl mx-auto text-lg" })}>{t("stats.section_subtitle")}</p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 lg:gap-8">
          {stats.map((stat, index) => {
            return (
              <Card
                key={stat.key}
                className="group border-none shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white/80 dark:bg-default-100/50 backdrop-blur-sm"
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <CardBody className="p-8 text-center relative overflow-hidden">
                  {/* Background decoration */}
                  <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/20 dark:to-secondary-900/20 rounded-full blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* Icon */}
                  <div className="relative z-10 mb-4">
                    <div
                      className={`w-12 h-12 mx-auto ${stat.bgColor} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}
                    >
                      <Icon icon={stat.icon} className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>

                  {/* Value */}
                  <div className="relative z-10 mb-3">
                    <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent group-hover:scale-105 transition-transform duration-300">
                      {stat.value}
                    </div>
                  </div>

                  {/* Label */}
                  <div className="relative z-10">
                    <p className="text-sm lg:text-base text-default-600 font-medium leading-relaxed">
                      {t(stat.labelKey)}
                    </p>
                  </div>

                  {/* Hover gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-secondary-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg" />
                </CardBody>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}
