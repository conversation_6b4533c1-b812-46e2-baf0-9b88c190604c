"use client";

import { <PERSON> } from "@heroui/react";
import { <PERSON><PERSON>, Card, CardBody, Image } from "@heroui/react";
import NextImage from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { subtitle, title } from "@/components/primitives";
import { featuredTemplates } from "@/lib/constants";

export default function TemplatesShowcase() {
  const t = useTranslations("homepage");

  return (
    <section
      className="py-20 md:py-32 bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-primary-900/10 dark:via-background dark:to-secondary-900/10 relative overflow-hidden"
      id="templates"
    >
      {/* Background decorations */}
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-10 left-1/4 w-32 h-32 bg-primary-200 dark:bg-primary-800/20 rounded-full blur-3xl opacity-20" />
        <div className="absolute bottom-10 right-1/4 w-40 h-40 bg-secondary-200 dark:bg-secondary-800/20 rounded-full blur-3xl opacity-20" />
      </div>

      <div className="max-w-6xl mx-auto px-4 relative z-10">
        <div className="text-center mb-20">
          <h2 className={title({ size: "md" })}>{t("templates.section_title")}</h2>
          <p className={subtitle({ class: "mt-6 max-w-3xl mx-auto text-lg" })}>{t("templates.section_subtitle")}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {featuredTemplates.map((template, index) => (
            <Card
              key={template.id}
              className="group border-none shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 bg-white/90 dark:bg-default-100/90 backdrop-blur-sm"
              style={{
                animationDelay: `${index * 150}ms`,
              }}
            >
              <CardBody className="p-0 relative overflow-hidden">
                <div className="relative rounded-t-lg overflow-hidden">
                  <Image
                    isBlurred
                    isZoomed
                    alt={`${template.name} resume template preview`}
                    as={NextImage}
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                    height={500}
                    radius="none"
                    src={`/assets/images/templates/${template.id}.jpg`}
                    width={400}
                  />
                  {/* Overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>

                <div className="p-6 relative">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-bold text-default-900 group-hover:text-primary-600 transition-colors duration-300">
                      {template.name}
                    </h3>
                    <Chip
                      color="success"
                      size="sm"
                      variant="flat"
                      className="shadow-sm group-hover:shadow-md transition-shadow duration-300"
                    >
                      ATS Optimized
                    </Chip>
                  </div>

                  <p className="text-default-600 mb-6 leading-relaxed">{t(`templates.descriptions.${template.id}`)}</p>

                  <div className="flex items-center justify-between">
                    <Button
                      as={Link}
                      color="primary"
                      href={`/templates/${template.id}`}
                      size="sm"
                      variant="flat"
                      className="group-hover:variant-solid group-hover:shadow-lg transition-all duration-300 font-semibold"
                    >
                      {t("templates.preview")}
                    </Button>
                  </div>

                  {/* Subtle hover gradient */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-500/3 to-secondary-500/3 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                </div>
              </CardBody>
            </Card>
          ))}
        </div>

        {/* Enhanced CTA button */}
        <div className="text-center">
          <Button
            as={Link}
            className="font-semibold shadow-lg hover:shadow-xl transition-all duration-300 px-8 py-6 text-lg"
            color="primary"
            href="/templates"
            size="lg"
            variant="solid"
          >
            {t("templates.view_all")}
          </Button>
        </div>
      </div>
    </section>
  );
}
