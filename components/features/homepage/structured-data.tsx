import { getTranslations } from "next-intl/server";

export default async function StructuredData() {
  const t = await getTranslations("homepage.seo");

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    name: "<PERSON><PERSON><PERSON>",
    description: t("description"),
    url: "https://quickcv.app",
    applicationCategory: "BusinessApplication",
    operatingSystem: "Web",
    offers: {
      "@type": "Offer",
      price: "100",
      priceCurrency: "USD",
      description: "Professional resume builder with lifetime access",
    },
    creator: {
      "@type": "Organization",
      name: "QuickCV",
      url: "https://quickcv.app",
    },
    featureList: [
      "ATS-optimized resume templates",
      "Multi-language support",
      "Real-time editing",
      "PDF export",
      "Auto-save functionality",
      "Professional templates",
    ],
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.9",
      ratingCount: "2847",
      bestRating: "5",
      worstRating: "1",
    },
    review: [
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: {
          "@type": "Person",
          name: "<PERSON>",
        },
        reviewBody: "QuickCV helped me land my dream job! The ATS optimization feature is a game-changer.",
      },
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: {
          "@type": "Person",
          name: "Ahmed Hassan",
        },
        reviewBody: "The multilingual support and RTL layout made it perfect for my Arabic resume.",
      },
    ],
  };

  return <script dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }} type="application/ld+json" />;
}
