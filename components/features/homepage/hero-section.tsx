"use client";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import CreateResumeButton from "@/components/features/resume/createResumeButton";
import { subtitle, title } from "@/components/primitives";

export default function HeroSection() {
  const t = useTranslations("homepage");

  return (
    <section className="relative flex flex-col items-center justify-center gap-8 py-20 md:py-32 overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-primary-950/20 dark:via-background dark:to-secondary-950/20" />
      <div className="absolute top-20 left-10 w-72 h-72 bg-primary-100 dark:bg-primary-900/20 rounded-full blur-3xl opacity-30" />
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-secondary-100 dark:bg-secondary-900/20 rounded-full blur-3xl opacity-20" />

      <div className="relative text-center max-w-5xl z-10">
        {/* Enhanced badge with animation */}
        <div className="mb-8 animate-in fade-in slide-in-from-top duration-700">
          <Chip
            className="mb-6 shadow-lg border border-primary-200 dark:border-primary-800"
            color="primary"
            variant="flat"
            startContent={<Icon icon="heroicons:sparkles-20-solid" className="w-4 h-4" />}
          >
            {t("hero.badge")}
          </Chip>
        </div>

        {/* Enhanced heading with better spacing */}
        <div className="animate-in fade-in slide-in-from-bottom duration-700 delay-200">
          <h1 className={title({ size: "lg", class: "leading-tight" })}>
            {t("hero.title_part1")}{" "}
            <span className={title({ color: "violet", size: "lg", class: "relative" })}>
              {t("hero.title_highlight")}
              <div className="absolute -top-2 -right-2">
                <Icon icon="heroicons:star-20-solid" className="w-6 h-6 text-yellow-400 animate-pulse" />
              </div>
            </span>{" "}
            {t("hero.title_part2")}
          </h1>
        </div>

        {/* Enhanced subtitle */}
        <div className="animate-in fade-in slide-in-from-bottom duration-700 delay-300">
          <p className={subtitle({ class: "mt-8 max-w-3xl mx-auto text-lg leading-relaxed" })}>{t("hero.subtitle")}</p>
        </div>

        {/* Enhanced CTA buttons */}
        <div className="animate-in fade-in slide-in-from-bottom duration-700 delay-500">
          <div className="flex flex-col sm:flex-row gap-4 justify-center mt-10">
            <CreateResumeButton
              size="lg"
              className="shadow-lg hover:shadow-xl transition-all duration-300 font-semibold px-8 py-3"
            />
            <Button
              as="a"
              className="font-semibold shadow-md hover:shadow-lg transition-all duration-300 px-8 py-3"
              href="#templates"
              size="lg"
              variant="bordered"
            >
              {t("hero.view_templates")}
            </Button>
          </div>
        </div>

        {/* Enhanced feature list with icons */}
        <div className="animate-in fade-in slide-in-from-bottom duration-700 delay-700">
          <div className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-6 lg:gap-8">
            <div className="flex items-center gap-3 px-4 py-2 rounded-full bg-white/60 dark:bg-default-100/60 backdrop-blur-sm shadow-sm">
              <div className="flex items-center justify-center w-6 h-6 bg-success-100 dark:bg-success-900/20 rounded-full">
                <Icon icon="heroicons:check-20-solid" className="w-4 h-4 text-success-600" />
              </div>
              <span className="text-sm font-medium text-default-700">{t("hero.feature_1")}</span>
            </div>
            <div className="flex items-center gap-3 px-4 py-2 rounded-full bg-white/60 dark:bg-default-100/60 backdrop-blur-sm shadow-sm">
              <div className="flex items-center justify-center w-6 h-6 bg-success-100 dark:bg-success-900/20 rounded-full">
                <Icon icon="heroicons:check-20-solid" className="w-4 h-4 text-success-600" />
              </div>
              <span className="text-sm font-medium text-default-700">{t("hero.feature_2")}</span>
            </div>
            <div className="flex items-center gap-3 px-4 py-2 rounded-full bg-white/60 dark:bg-default-100/60 backdrop-blur-sm shadow-sm">
              <div className="flex items-center justify-center w-6 h-6 bg-success-100 dark:bg-success-900/20 rounded-full">
                <Icon icon="heroicons:check-20-solid" className="w-4 h-4 text-success-600" />
              </div>
              <span className="text-sm font-medium text-default-700">{t("hero.feature_3")}</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
