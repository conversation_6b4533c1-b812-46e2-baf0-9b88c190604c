import { Card, CardBody } from "@heroui/react";
import { getTranslations } from "next-intl/server";
import { subtitle, title } from "@/components/primitives";
import { features } from "@/lib/constants";

export default async function FeaturesSection() {
  const t = await getTranslations("homepage");

  return (
    <section className="py-20 md:py-32 bg-gradient-to-b from-white to-default-50 dark:from-background dark:to-default-950/20">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-20">
          <h2 className={title({ size: "md" })}>{t("features.section_title")}</h2>
          <p className={subtitle({ class: "mt-6 max-w-3xl mx-auto text-lg" })}>{t("features.section_subtitle")}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card
              key={feature.key}
              className="group border-none shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white/80 dark:bg-default-100/50 backdrop-blur-sm"
              style={{
                animationDelay: `${index * 100}ms`,
              }}
            >
              <CardBody className="p-8 text-start relative overflow-hidden">
                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/20 dark:to-secondary-900/20 rounded-full blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* Icon with enhanced styling */}
                <div className="relative z-10 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-2xl flex items-center justify-center text-3xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    {feature.icon}
                  </div>
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <h3 className="text-xl font-bold mb-3 text-default-900 group-hover:text-primary-600 transition-colors duration-300">
                    {t(feature.titleKey)}
                  </h3>
                  <p className="text-default-600 leading-relaxed">{t(feature.descriptionKey)}</p>
                </div>

                {/* Hover gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-secondary-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg" />
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
