"use client";
import { <PERSON><PERSON>, <PERSON>, CardBody } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import CreateResumeButton from "@/components/features/resume/createResumeButton";
import { subtitle, title } from "@/components/primitives";

export default function CTASection() {
  const t = useTranslations("homepage");

  return (
    <section className="py-20 md:py-32 bg-gradient-to-b from-white to-default-50 dark:from-background dark:to-default-950/20">
      <div className="max-w-5xl mx-auto px-4">
        <Card className="border-none shadow-2xl bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600 text-white relative overflow-hidden">
          {/* Background decorations */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary-600/90 to-secondary-600/90" />
          <div className="absolute top-0 right-0 w-96 h-96 bg-white/10 rounded-full blur-3xl opacity-30 -translate-y-1/2 translate-x-1/2" />
          <div className="absolute bottom-0 left-0 w-80 h-80 bg-white/5 rounded-full blur-3xl opacity-40 translate-y-1/2 -translate-x-1/2" />

          <CardBody className="p-12 lg:p-16 text-center relative z-10">
            {/* Icon */}
            <div className="mb-8">
              <div className="w-16 h-16 mx-auto bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <Icon icon="heroicons:rocket-launch-20-solid" className="w-8 h-8 text-white" />
              </div>
            </div>

            {/* Title */}
            <h2 className={title({ size: "md", class: "text-white mb-6" })}>{t("cta.title")}</h2>

            {/* Subtitle */}
            <p className={subtitle({ class: "mt-6 text-white/90 max-w-2xl mx-auto text-lg leading-relaxed" })}>
              {t("cta.subtitle")}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-10">
              <CreateResumeButton
                className="bg-white text-primary-600 hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 px-8 py-3"
                size="lg"
              />
              <Button
                as="a"
                className="border-white/80 text-white hover:bg-white/10 font-semibold shadow-md hover:shadow-lg transition-all duration-300 px-8 py-3"
                href="/templates"
                size="lg"
                variant="bordered"
              >
                {t("cta.explore_templates")}
              </Button>
            </div>

            {/* Benefits */}
            <div className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-6 lg:gap-8">
              <div className="flex items-center gap-3 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm">
                <div className="flex items-center justify-center w-6 h-6 bg-white/20 rounded-full">
                  <Icon icon="heroicons:check-20-solid" className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium text-white/95">{t("cta.benefit_1")}</span>
              </div>
              <div className="flex items-center gap-3 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm">
                <div className="flex items-center justify-center w-6 h-6 bg-white/20 rounded-full">
                  <Icon icon="heroicons:check-20-solid" className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium text-white/95">{t("cta.benefit_2")}</span>
              </div>
              <div className="flex items-center gap-3 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm">
                <div className="flex items-center justify-center w-6 h-6 bg-white/20 rounded-full">
                  <Icon icon="heroicons:check-20-solid" className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium text-white/95">{t("cta.benefit_3")}</span>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </section>
  );
}
