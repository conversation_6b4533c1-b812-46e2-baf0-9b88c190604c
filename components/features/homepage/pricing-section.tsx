"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { UpgradeModal } from "@/components/payment";

export default function PricingSection() {
  const t = useTranslations("pricing_page");
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);

  return (
    <section className="py-20 bg-gradient-to-br from-primary-50/30 via-background to-secondary-50/30 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-secondary/10 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 max-w-6xl relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Icon icon="heroicons:sparkles-20-solid" className="w-4 h-4" />
            {t("limited_offer")}
          </div>
          <h2 className="text-3xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            {t("title")}
          </h2>
          <p className="text-lg text-default-600 max-w-2xl mx-auto leading-relaxed">{t("subtitle")}</p>
        </div>

        {/* Main Pricing Card */}
        <div className="flex justify-center mb-16">
          <Card className="max-w-lg w-full relative shadow-2xl border border-primary/20 bg-gradient-to-b from-background to-default-50/50 backdrop-blur-sm">
            <CardHeader className="text-center pb-6 pt-12 flex justify-center items-center">
              <div>
                <h3 className="text-2xl md:text-3xl font-bold mb-2">{t("lifetime_plan.title")}</h3>
                <div className="mt-6 mb-4">
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-6xl md:text-7xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                      {t("lifetime_plan.price")}
                    </span>
                  </div>
                </div>
                <p className="text-default-600 font-medium text-lg">{t("lifetime_plan.subtitle")}</p>
              </div>
            </CardHeader>

            <CardBody className="pt-0 px-8 pb-8">
              {/* Features List */}
              <div className="space-y-4 mb-8">
                {t.raw("lifetime_plan.features").map((feature: string, index: number) => (
                  <div key={index} className="flex items-start gap-3 group">
                    <div className="bg-success/10 rounded-full p-1 mt-0.5 group-hover:bg-success/20 transition-colors">
                      <Icon icon="heroicons:check-20-solid" className="w-4 h-4 text-success" />
                    </div>
                    <span className="text-default-700 leading-relaxed">{feature}</span>
                  </div>
                ))}
              </div>

              {/* CTA Button */}
              <Button
                color="primary"
                size="lg"
                className="w-full h-14 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-primary to-secondary"
                onPress={() => setIsUpgradeModalOpen(true)}
                startContent={<Icon icon="heroicons:rocket-launch-20-solid" className="w-5 h-5" />}
              >
                {t("lifetime_plan.cta")}
              </Button>

              {/* Trust indicators */}
              <div className="flex items-center justify-center gap-4 mt-4 text-xs text-default-500">
                <div className="flex items-center gap-1">
                  <Icon icon="heroicons:shield-check-20-solid" className="w-4 h-4 text-success" />
                  {t("secure_payment")}
                </div>
                <div className="flex items-center gap-1">
                  <Icon icon="heroicons:clock-20-solid" className="w-4 h-4 text-primary" />
                  {t("instant_access")}
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Value Proposition Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <Card className="shadow-lg border border-default-200/50 hover:shadow-xl transition-all duration-300 group">
            <CardBody className="p-8">
              <div className="flex items-start gap-4">
                <div className="bg-warning/10 rounded-full p-3 group-hover:bg-warning/20 transition-colors">
                  <Icon icon="heroicons:chart-bar-20-solid" className="w-6 h-6 text-warning" />
                </div>
                <div>
                  <h4 className="text-xl font-bold mb-3 text-default-900">{t("comparison.vs_monthly.title")}</h4>
                  <p className="text-default-600 leading-relaxed">{t("comparison.vs_monthly.description")}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="shadow-lg border border-default-200/50 hover:shadow-xl transition-all duration-300 group">
            <CardBody className="p-8">
              <div className="flex items-start gap-4">
                <div className="bg-success/10 rounded-full p-3 group-hover:bg-success/20 transition-colors">
                  <Icon icon="heroicons:gift-20-solid" className="w-6 h-6 text-success" />
                </div>
                <div>
                  <h4 className="text-xl font-bold mb-3 text-default-900">{t("comparison.vs_free.title")}</h4>
                  <p className="text-default-600 leading-relaxed">{t("comparison.vs_free.description")}</p>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Money-Back Guarantee */}
        <div className="text-center">
          <Card className="max-w-2xl mx-auto shadow-lg bg-gradient-to-r from-success/5 to-success/10 border border-success/20">
            <CardBody className="p-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="bg-success/10 rounded-full p-2">
                  <Icon icon="heroicons:shield-check-20-solid" className="w-8 h-8 text-success" />
                </div>
                <h4 className="text-2xl font-bold text-success">{t("guarantee.title")}</h4>
              </div>
              <p className="text-default-600 text-lg leading-relaxed">{t("guarantee.description")}</p>
            </CardBody>
          </Card>
        </div>

        {/* Upgrade Modal */}
        <UpgradeModal isOpen={isUpgradeModalOpen} onClose={() => setIsUpgradeModalOpen(false)} />
      </div>
    </section>
  );
}
