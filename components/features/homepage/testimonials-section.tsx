"use client";
import { Avatar } from "@heroui/react";
import { Card, CardBody } from "@heroui/card";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { subtitle, title } from "@/components/primitives";
import { testimonials } from "@/lib/constants";

export default function TestimonialsSection() {
  const t = useTranslations("homepage");

  return (
    <section className="py-20 md:py-32 bg-gradient-to-b from-white to-default-50 dark:from-background dark:to-default-950/20 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-1/3 w-64 h-64 bg-primary-100 dark:bg-primary-900/10 rounded-full blur-3xl opacity-20" />
        <div className="absolute bottom-20 right-1/3 w-80 h-80 bg-secondary-100 dark:bg-secondary-900/10 rounded-full blur-3xl opacity-15" />
      </div>

      <div className="max-w-6xl mx-auto px-4 relative z-10">
        <div className="text-center mb-20">
          <h2 className={title({ size: "md" })}>{t("testimonials.section_title")}</h2>
          <p className={subtitle({ class: "mt-6 max-w-3xl mx-auto text-lg" })}>{t("testimonials.section_subtitle")}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card
              key={index}
              className="group border-none shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white/80 dark:bg-default-100/50 backdrop-blur-sm"
              style={{
                animationDelay: `${index * 150}ms`,
              }}
            >
              <CardBody className="p-8 relative overflow-hidden">
                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/20 dark:to-secondary-900/20 rounded-full blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* Quote icon */}
                <div className="relative z-10 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon icon="heroicons:chat-bubble-left-20-solid" className="w-6 h-6 text-primary-600" />
                  </div>
                </div>

                {/* Rating */}
                <div className="relative z-10 flex mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Icon
                      key={i}
                      icon="heroicons:star-20-solid"
                      className="w-5 h-5 text-yellow-400 group-hover:text-yellow-500 transition-colors duration-300"
                    />
                  ))}
                </div>

                {/* Content */}
                <div className="relative z-10 mb-8">
                  <p className="text-default-700 leading-relaxed text-lg italic">&ldquo;{testimonial.content}&rdquo;</p>
                </div>

                {/* Author */}
                <div className="relative z-10 flex items-center gap-4">
                  <Avatar
                    className="bg-gradient-to-br from-primary-500 to-secondary-500 ring-2 ring-white/50 group-hover:ring-primary-200 transition-all duration-300"
                    name={testimonial.name}
                    size="md"
                  />
                  <div>
                    <p className="font-bold text-default-900 group-hover:text-primary-600 transition-colors duration-300">
                      {testimonial.name}
                    </p>
                    <p className="text-sm text-default-600 font-medium">
                      {testimonial.role} at {testimonial.company}
                    </p>
                  </div>
                </div>

                {/* Hover gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary-500/3 to-secondary-500/3 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg" />
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
