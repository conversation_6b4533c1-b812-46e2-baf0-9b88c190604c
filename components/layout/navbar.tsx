"use client";

import { SignedIn, SignedOut, SignInButton } from "@clerk/nextjs";
import {
  Navbar as Hero<PERSON>Navbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  NavbarMenu,
  NavbarMenuItem,
  NavbarMenuToggle,
} from "@heroui/navbar";
import { Avatar, Badge, Button, Chip, Divider, Link, Tooltip } from "@heroui/react";
import { Icon } from "@iconify/react";
import NextLink from "next/link";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import CreateResumeButton from "@/components/features/resume/createResumeButton";
import { LanguageSwitcher, ThemeSwitch } from "@/components/shared";
import { Logo } from "@/components/shared/common/icons";
import { CustomUserButton } from "@/components/shared/custom-user-button";
import { getMainNavigationItems, getUserMenuItems } from "@/config/navbar-menu";
import { getMenuItemColorsFromItem } from "@/lib/utils/colors";
import { UserDropdown } from "./user-dropdown";

export const Navbar = () => {
  const t = useTranslations("navigation");
  const tResumes = useTranslations("resumes");
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Check if link is active
  const isActive = (path: string) => {
    const cleanPathname = pathname.replace(/^\/(en|ar)/, "");
    const cleanPath = path.replace(/^\/(en|ar)/, "");
    return cleanPathname === cleanPath || cleanPathname.startsWith(cleanPath + "/");
  };

  const menuItems = getMainNavigationItems();
  const userMenuItems = getUserMenuItems();

  return (
    <>
      <HeroUINavbar
        maxWidth="xl"
        position="sticky"
        isMenuOpen={isMenuOpen}
        onMenuOpenChange={setIsMenuOpen}
        className={`
					transition-all duration-300 backdrop-blur-lg
					${
            scrolled
              ? "bg-white/80 dark:bg-gray-900/80 shadow-lg border-b border-gray-200/20 dark:border-gray-700/20"
              : "bg-transparent"
          }
				`}
      >
        <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
          <NavbarBrand as="li" className="gap-3 max-w-fit">
            <NextLink
              className="flex justify-start items-center gap-2 group"
              href="/"
              onClick={() => setIsMenuOpen(false)}
            >
              <div className="relative">
                <Logo className="transition-transform group-hover:scale-110" />
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg blur opacity-0 group-hover:opacity-30 transition-opacity" />
              </div>
              <p className="font-bold text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                QuickCV
              </p>
            </NextLink>
          </NavbarBrand>

          <ul className="hidden lg:flex gap-2 justify-start items-center ms-8">
            {menuItems.map((item) => {
              if (item.signedInOnly) {
                return (
                  <SignedIn key={item.href}>
                    <NavbarItem>
                      <Link
                        as={NextLink}
                        href={item.href}
                        className={`
													px-3 py-2 rounded-lg transition-all duration-200 flex items-center gap-2
													${
                            isActive(item.href)
                              ? "bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-600 dark:text-blue-400 font-medium"
                              : "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800"
                          }
												`}
                      >
                        <Icon icon={item.icon} className="w-4 h-4" />
                        {t(item.translationKey)}
                      </Link>
                    </NavbarItem>
                  </SignedIn>
                );
              }
              return (
                <NavbarItem key={item.href}>
                  <Link
                    as={NextLink}
                    href={item.href}
                    className={`
											px-3 py-2 rounded-lg transition-all duration-200 flex items-center gap-2
											${
                        isActive(item.href)
                          ? "bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-600 dark:text-blue-400 font-medium"
                          : "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800"
                      }
										`}
                  >
                    <Icon icon={item.icon} className="w-4 h-4" />
                    {t(item.translationKey)}
                  </Link>
                </NavbarItem>
              );
            })}
            <SignedIn>
              <NavbarItem>
                <CreateResumeButton
                  size="sm"
                  text={tResumes("create_resume")}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
                />
              </NavbarItem>
            </SignedIn>
          </ul>
        </NavbarContent>

        <NavbarContent className="hidden sm:flex basis-1/5 sm:basis-full" justify="end">
          <NavbarItem className="hidden sm:flex gap-2 items-center">
            <Tooltip content="Toggle theme">
              <div className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                <ThemeSwitch />
              </div>
            </Tooltip>
            <LanguageSwitcher />
          </NavbarItem>

          <NavbarItem className="hidden md:flex">
            <SignedOut>
              <SignInButton mode="modal">
                <Button
                  color="primary"
                  variant="shadow"
                  className="bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium"
                  startContent={<Icon icon="tabler:login" className="w-4 h-4" />}
                >
                  {t("signin")}
                </Button>
              </SignInButton>
            </SignedOut>
            <SignedIn>
              <UserDropdown trigger={<CustomUserButton size="sm" />} />
            </SignedIn>
          </NavbarItem>
        </NavbarContent>

        {/* Mobile Navigation */}
        <NavbarContent className="sm:hidden basis-1 pl-4" justify="end">
          <SignedOut>
            <SignInButton mode="modal">
              <Button
                color="primary"
                size="sm"
                variant="shadow"
                className="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
                isIconOnly
              >
                <Icon icon="tabler:login" className="w-4 h-4" />
              </Button>
            </SignInButton>
          </SignedOut>
          <SignedIn>
            <CustomUserButton size="sm" />
          </SignedIn>
          <NavbarMenuToggle
            className="text-gray-700 dark:text-gray-300"
            icon={(isOpen) =>
              isOpen ? <Icon icon="tabler:x" className="w-6 h-6" /> : <Icon icon="tabler:menu-2" className="w-6 h-6" />
            }
          />
        </NavbarContent>

        {/* Mobile Menu */}
        <NavbarMenu className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl pt-6">
          <div className="flex flex-col gap-4">
            {/* Theme and Language Switchers */}
            <div className="flex items-center justify-between px-4 pb-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <Tooltip content="Toggle theme">
                  <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800">
                    <ThemeSwitch />
                  </div>
                </Tooltip>
                <LanguageSwitcher />
              </div>
            </div>

            {/* Menu Items */}
            <div className="flex flex-col gap-2 px-4">
              {menuItems.map((item, index) => {
                if (item.signedInOnly) {
                  return (
                    <SignedIn key={item.href}>
                      <NavbarMenuItem>
                        <Link
                          as={NextLink}
                          href={item.href}
                          className={`
														w-full px-4 py-3 rounded-xl flex items-center gap-3 transition-all duration-200
														${
                              isActive(item.href)
                                ? "bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-600 dark:text-blue-400 font-medium"
                                : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                            }
													`}
                          onPress={() => setIsMenuOpen(false)}
                        >
                          {(() => {
                            const colors = getMenuItemColorsFromItem(item);
                            return (
                              <div
                                className={`w-10 h-10 rounded-lg flex items-center justify-center ${colors.background}`}
                              >
                                <Icon icon={item.icon} className={`w-5 h-5 ${colors.text}`} />
                              </div>
                            );
                          })()}
                          <div className="flex-1">
                            <p className="font-medium">{t(item.translationKey)}</p>
                          </div>
                          <Icon icon="tabler:chevron-right" className="w-5 h-5 opacity-50" />
                        </Link>
                      </NavbarMenuItem>
                    </SignedIn>
                  );
                }
                return (
                  <NavbarMenuItem key={item.href}>
                    <Link
                      as={NextLink}
                      href={item.href}
                      className={`
												w-full px-4 py-3 rounded-xl flex items-center gap-3 transition-all duration-200
												${
                          isActive(item.href)
                            ? "bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-600 dark:text-blue-400 font-medium"
                            : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                        }
											`}
                      onPress={() => setIsMenuOpen(false)}
                    >
                      {(() => {
                        const colors = getMenuItemColorsFromItem(item);
                        return (
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${colors.background}`}>
                            <Icon icon={item.icon} className={`w-5 h-5 ${colors.text}`} />
                          </div>
                        );
                      })()}
                      <div className="flex-1">
                        <p className="font-medium">{t(item.translationKey)}</p>
                      </div>
                      <Icon icon="tabler:chevron-right" className="w-5 h-5 opacity-50" />
                    </Link>
                  </NavbarMenuItem>
                );
              })}
            </div>

            {/* Create Resume Button */}
            <SignedIn>
              <div className="px-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <CreateResumeButton
                  text={tResumes("create_resume")}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg"
                />
              </div>
            </SignedIn>

            {/* User Section for Mobile */}
            <SignedIn>
              <div className="px-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="space-y-2">
                  {userMenuItems.map((item) => (
                    <Link
                      key={item.key}
                      href={item.href}
                      className="w-full px-4 py-3 rounded-lg flex items-center gap-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      onPress={() => setIsMenuOpen(false)}
                    >
                      <Icon icon={item.icon} className="w-5 h-5" />
                      <span>{t(item.translationKey)}</span>
                    </Link>
                  ))}
                </div>
              </div>
            </SignedIn>
          </div>
        </NavbarMenu>
      </HeroUINavbar>

      {/* Animated gradient line */}
      <div
        className="h-[2px] w-full bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-0 transition-opacity duration-300"
        style={{ opacity: scrolled ? 0.5 : 0 }}
      />
    </>
  );
};
