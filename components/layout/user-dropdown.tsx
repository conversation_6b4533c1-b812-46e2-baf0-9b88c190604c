"use client";

import { useClerk } from "@clerk/nextjs";
import {
  Avatar,
  Button,
  Chip,
  Divider,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownSection,
  DropdownTrigger,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { getMenuItemsBySection, getUserMenuItems, USER_MENU_SECTIONS } from "@/config/navbar-menu";
import { useCurrentUser } from "@/hooks/use-current-user";

interface UserDropdownProps {
  trigger: React.ReactNode;
}

export function UserDropdown({ trigger }: UserDropdownProps) {
  const t = useTranslations("navigation");
  const tCommon = useTranslations("common");
  const { user, isPremium, isLoading } = useCurrentUser();
  const { signOut } = useClerk();

  const menuItemsBySection = getMenuItemsBySection("user");

  // Helper function to render premium chip
  const renderPremiumChip = () => (
    <Chip size="sm" variant="flat" className="bg-amber-100 text-amber-800 text-xs">
      {t("premium")}
    </Chip>
  );

  // Helper function to render a dropdown section
  const renderSection = (sectionName: string, items: any[], showDivider = true) => {
    if (!items?.length) return null;

    return (
      <DropdownSection showDivider={showDivider}>
        {items.map((item) => (
          <DropdownItem
            key={item.key}
            startContent={<Icon icon={item.icon} className="w-4 h-4" />}
            href={item.href}
            endContent={item.requiresPremium && !isPremium ? renderPremiumChip() : null}
          >
            {t(item.translationKey)}
          </DropdownItem>
        ))}
      </DropdownSection>
    );
  };

  return (
    <Dropdown placement="bottom-end">
      <DropdownTrigger asChild>{trigger}</DropdownTrigger>
      <DropdownMenu aria-label={t("user_menu")} className="w-64">
        <DropdownSection showDivider>
          <DropdownItem key="user-info" isReadOnly className="h-14 gap-2 opacity-100" textValue="User information">
            <div className="flex items-center gap-3">
              <Avatar name={user?.emailAddress} size="sm" className="flex-shrink-0" />
              <div className="flex flex-col flex-1 min-w-0">
                <p className="text-sm font-medium text-default-900 truncate">{user?.emailAddress || t("user")}</p>
                <div className="flex items-center gap-2">
                  <p className="text-xs text-default-500 truncate">{user?.emailAddress}</p>
                  {isPremium && (
                    <Chip
                      size="sm"
                      variant="flat"
                      className="bg-gradient-to-r from-amber-400 to-orange-500 text-white text-xs px-1 h-4"
                    >
                      ✨ {t("premium")}
                    </Chip>
                  )}
                </div>
              </div>
            </div>
          </DropdownItem>
        </DropdownSection>

        <>{USER_MENU_SECTIONS.map((section) => renderSection(section, menuItemsBySection[section]))}</>

        {/* Sign Out Section */}
        <DropdownSection>
          <DropdownItem
            key="sign-out"
            startContent={<Icon icon="tabler:logout" className="w-4 h-4" />}
            className="text-danger"
            color="danger"
            onPress={() => {
              signOut({ redirectUrl: "/" });
            }}
          >
            {tCommon("sign_out")}
          </DropdownItem>
        </DropdownSection>
      </DropdownMenu>
    </Dropdown>
  );
}
