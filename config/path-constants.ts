import { useLocale } from "@/hooks/use-locale";

export const routes = {
  resumeEditPath: (id: number, locale: string = "ar") => `/${locale}/resumes/edit/${id}`,
  resumesPath: (locale: string = "ar") => `/${locale}/resumes`,
  resumePath: (id: number, locale: string = "ar") => `/${locale}/resumes/${id}`,
  fetchResumePath: (id: number, locale: string = "ar") => `/${locale}/resumes/${id}`,
  fetchResumesPath: (locale: string = "ar") => `/${locale}/resumes`,
};

// Client-side route helpers that automatically use current locale
export const useRoutes = () => {
  const locale = useLocale();

  return {
    resumeEditPath: (id: number) => routes.resumeEditPath(id, locale),
    resumesPath: () => routes.resumesPath(locale),
    resumePath: (id: number) => routes.resumePath(id, locale),
    fetchResumePath: (id: number) => routes.fetchResumePath(id, locale),
    fetchResumesPath: () => routes.fetchResumesPath(locale),
  };
};
