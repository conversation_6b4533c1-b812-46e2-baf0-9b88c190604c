import { useTranslations } from "next-intl";

// Translation-aware schemas that use next-intl hooks
export const useSchemas = () => {
  const t = useTranslations("forms");

  const referenceSchema = {
    type: "object",
    collection: "references",
    description: t("sections.references"),
    entity: "reference",
    properties: {
      name: { type: "string", placeholder: t("full_name") },
      company: { type: "string", placeholder: t("company") },
      position: { type: "string", placeholder: t("position") },
      email: { type: "string", placeholder: t("placeholders.email_address") },
      phone: { type: "string", placeholder: t("placeholders.phone_number") },
      description: { type: "textarea", label: t("description") },
    },
    required: ["name", "email"],
  };

  const projectSchema = {
    type: "object",
    collection: "projects",
    description: t("sections.projects"),
    entity: "project",
    properties: {
      title: { type: "string", placeholder: t("placeholders.project_title") },
      client: { type: "string", placeholder: t("placeholders.client") },
      startDate: { type: "date", placeholder: t("placeholders.from") },
      endDate: { type: "date", placeholder: t("placeholders.to") },
      url: {
        type: ["string", "null"],
        placeholder: t("placeholders.url_example"),
      },
      description: { type: "textarea", label: t("description") },
    },
    required: ["title"],
  };

  const languageSchema = {
    type: "object",
    collection: "languages",
    description: t("sections.languages"),
    entity: "language",
    properties: {
      name: { type: "string", placeholder: t("language_name") },
      proficiency: {
        type: "number",
        placeholder: t("placeholders.proficiency_level"),
      },
    },
    required: ["name"],
  };

  const hobbySchema = {
    type: "object",
    collection: "hobbies",
    description: t("sections.hobbies"),
    entity: "hobby",
    properties: {
      name: {
        type: "string",
        placeholder: t("hobby_name"),
        className: "col-span-full",
      },
    },
    required: ["name"],
  };

  const experienceSchema = {
    type: "object",
    collection: "experiences",
    description: t("sections.experiences"),
    entity: "experience",
    properties: {
      company: { type: "string", label: t("placeholders.company_name") },
      title: { type: "string", label: t("jobTitle") },
      city: { type: "string", label: t("city") },
      country: { type: "string", label: t("country") },
      startDate: { type: "date", label: t("placeholders.from") },
      endDate: { type: "date", label: t("placeholders.to") },
      is_current: { type: "boolean", label: t("labels.currently_employed") },
      description: { type: "textarea", label: t("summary") },
    },
  };

  const educationSchema = {
    type: "object",
    collection: "educations",
    description: t("sections.educations"),
    entity: "education",
    properties: {
      institution: {
        type: "string",
        label: t("placeholders.institution_name"),
      },
      field_of_study: {
        type: "string",
        label: t("fieldOfStudy"),
      },
      degree: {
        type: "string",
        label: t("degree"),
      },
      city: {
        type: "string",
        label: t("city"),
      },
      country: {
        type: "string",
        label: t("country"),
      },
      startDate: {
        type: "date",
        label: t("placeholders.from"),
      },
      endDate: {
        type: "date",
        label: t("placeholders.to"),
      },
      website: {
        type: "string",
        placeholder: t("placeholders.url_example"),
        label: t("website"),
      },
      is_current: {
        type: "boolean",
        label: t("labels.currently_study"),
      },
      description: {
        type: "textarea",
        label: t("summary"),
      },
    },
  };

  const certificationSchema = {
    type: "object",
    collection: "certifications",
    description: t("sections.certifications"),
    entity: "certification",
    properties: {
      title: {
        type: "string",
        placeholder: t("placeholders.certification_name"),
      },
      issuer: { type: "string", placeholder: t("placeholders.issuer") },
      url: {
        type: "string",
        placeholder: t("placeholders.url_example"),
      },
      date_received: {
        type: "date",
        placeholder: t("placeholders.date_of_certification"),
      },
      description: { type: "textarea", label: t("description") },
    },
    required: ["title", "issuer"],
  };

  const awardSchema = {
    type: "object",
    collection: "awards",
    description: t("sections.awards"),
    entity: "award",
    properties: {
      title: { type: "string", placeholder: t("placeholders.award_title") },
      issuer: { type: "string", placeholder: t("placeholders.issuer") },
      date_received: {
        type: "date",
        placeholder: t("placeholders.date_received"),
      },
      url: {
        type: ["string", "null"],
        placeholder: t("placeholders.url_example"),
      },
      description: { type: "textarea", label: t("description") },
    },
    required: ["title", "issuer"],
  };

  const volunteeringSchema = {
    type: "object",
    collection: "volunteerings",
    description: t("sections.volunteerings"),
    entity: "volunteering",
    properties: {
      organization: {
        type: "string",
        placeholder: t("placeholders.organization"),
      },
      role: { type: "string", placeholder: t("role") },
      startDate: { type: "date", placeholder: t("placeholders.from") },
      endDate: { type: "date", placeholder: t("placeholders.to") },
      description: { type: "textarea", label: t("description") },
    },
    required: ["organization", "role"],
  };

  const skillSchema = {
    type: "object",
    collection: "skills",
    description: t("sections.skills"),
    entity: "skill",
    properties: {
      name: { type: "string", placeholder: t("skill_name") },
      category: { type: "string", placeholder: t("category") },
      proficiency: {
        type: "number",
        placeholder: t("placeholders.proficiency_level"),
      },
    },
    required: ["name"],
  };

  const profilesSchema = {
    type: "object",
    collection: "profiles",
    description: t("sections.profiles"),
    entity: "profile",
    properties: {
      username: {
        type: "string",
        placeholder: t("placeholders.username"),
        label: t("username"),
      },
      url: {
        type: "string",
        placeholder: t("placeholders.github_url"),
        label: t("url"),
      },
      network: {
        type: "string",
        placeholder: t("placeholders.github"),
        label: t("network"),
      },
      icon: {
        type: "string",
        placeholder: t("placeholders.github_icon"),
        label: t("icon"),
        description: t("labels.icon_description"),
      },
    },
  };

  return {
    referenceSchema,
    projectSchema,
    languageSchema,
    hobbySchema,
    experienceSchema,
    educationSchema,
    certificationSchema,
    awardSchema,
    volunteeringSchema,
    skillSchema,
    profilesSchema,
  };
};
