export interface NavbarMenuItem {
  key: string;
  icon: string;
  href: string;
  translationKey: string;
  type: "main" | "user";
  requiresPremium?: boolean;
  signedInOnly?: boolean;
  section?: "account" | "content" | "support" | "public";
  color?: "primary" | "secondary" | "success" | "warning" | "danger";
}

export const NAVBAR_MENU_ITEMS: NavbarMenuItem[] = [
  // Main Navigation Items (shown in navbar)
  {
    key: "about",
    icon: "tabler:info-circle",
    href: "/about",
    translationKey: "about",
    type: "main",
    section: "public",
    color: "primary",
  },
  {
    key: "templates",
    icon: "tabler:layout-grid",
    href: "/templates",
    translationKey: "templates",
    type: "main",
    section: "public",
    color: "secondary",
  },
  {
    key: "resumes",
    icon: "tabler:file-text",
    href: "/resumes",
    translationKey: "resumes",
    type: "main",
    section: "content",
    color: "success",
    signedInOnly: true,
  },
  {
    key: "websites",
    icon: "tabler:world",
    href: "/websites",
    translationKey: "websites",
    type: "main",
    section: "content",
    color: "warning",
    signedInOnly: true,
  },

  // User Menu Items (shown in dropdown)
  {
    key: "profile",
    icon: "tabler:user",
    href: "/profile",
    translationKey: "profile",
    type: "user",
    section: "account",
  },
  {
    key: "billing",
    icon: "tabler:credit-card",
    href: "/billing",
    translationKey: "billing",
    type: "user",
    section: "account",
  },
  {
    key: "help",
    icon: "tabler:help",
    href: "/help",
    translationKey: "help",
    type: "user",
    section: "support",
  },
];

/**
 * Get main navigation items (for navbar)
 */
export function getMainNavigationItems() {
  return NAVBAR_MENU_ITEMS.filter((item) => item.type === "main");
}

/**
 * Get user menu items (for dropdown)
 */
export function getUserMenuItems() {
  return NAVBAR_MENU_ITEMS.filter((item) => item.type === "user");
}

/**
 * Get menu items grouped by section
 */
export function getMenuItemsBySection(type?: "main" | "user") {
  const items = type ? NAVBAR_MENU_ITEMS.filter((item) => item.type === type) : NAVBAR_MENU_ITEMS;
  const grouped = items.reduce(
    (acc, item) => {
      const section = item.section || "other";
      if (!acc[section]) {
        acc[section] = [];
      }
      acc[section].push(item);
      return acc;
    },
    {} as Record<string, NavbarMenuItem[]>,
  );

  return grouped;
}

/**
 * Get menu sections in display order for user menu
 */
export const USER_MENU_SECTIONS = ["account", "content", "support"] as const;
export type UserMenuSection = (typeof USER_MENU_SECTIONS)[number];
